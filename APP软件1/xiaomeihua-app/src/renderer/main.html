<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>小梅花AI智能客服</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
      user-select: none; /* 全局禁止文本选择 */
    }

    /* 【全局修复点击选中bug】移除所有元素的默认焦点轮廓和选择效果 */
    *:focus,
    *:active,
    *:focus-visible,
    *:focus-within {
      outline: 0 !important;
      outline-width: 0 !important;
      outline-style: none !important;
      outline-color: transparent !important;
      outline-offset: 0 !important;
      border: 0 !important;
      box-shadow: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }



    /* 【特殊针对店铺标签页】彻底移除所有可能的边框和轮廓 */
    .shop-item *,
    .shop-item *:focus,
    .shop-item *:active,
    .shop-item *:focus-visible,
    .shop-item *:focus-within,
    .shop-item *:hover {
      outline: 0 !important;
      outline-width: 0 !important;
      outline-style: none !important;
      outline-color: transparent !important;
      outline-offset: 0 !important;
      border: 0 !important;
      border-width: 0 !important;
      border-style: none !important;
      border-color: transparent !important;
      box-shadow: none !important;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      appearance: none !important;
    }

    /* 【强力修复悬停矩形框】针对所有可能的悬停状态 */
    .shop-item:hover,
    .shop-item:hover:not(.active),
    .shop-item:hover.active,
    .shop-item:hover::before,
    .shop-item:hover::after,
    .shop-item:hover:not(.active)::before,
    .shop-item:hover:not(.active)::after {
      outline: 0 !important;
      outline-width: 0 !important;
      outline-style: none !important;
      outline-color: transparent !important;
      outline-offset: 0 !important;
      border: 0 !important;
      border-width: 0 !important;
      border-style: none !important;
      border-color: transparent !important;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      appearance: none !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
    }

    /* 移除移动端和桌面端的点击高亮效果 */
    * {
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      -webkit-appearance: none !important;
      appearance: none !important;
    }
  
    body {
      background: #f5f5f5;
      color: #333;
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
      user-select: none; /* 禁止文本选择 */
    }
  
    /* 店铺信息栏 - 进一步增大尺寸 */
    .shop-info-bar {
      display: flex;
      background: #e8eaed; /* Chrome浏览器的浅灰色背景 */
      align-items: flex-end; /* 标签页底部对齐 */
      width: 100%;
      height: 52px; /* 进一步增大高度 */
      position: relative;
      z-index: 100;
      -webkit-app-region: drag;
      border-bottom: 1px solid #dadce0; /* Chrome风格的底部边框 */
      user-select: none;
      overflow: visible;
    }
  

  
    /* macOS使用系统原生红绿灯按钮，不需要自定义按钮样式 */

    /* Windows窗口控制按钮 - 适配新的高度 */
    .win-controls {
      position: absolute;
      top: 0;
      right: 0; /* 改为右上角 */
      display: flex;
      height: 52px; /* 与店铺菜单栏高度一致 */
      -webkit-app-region: no-drag;
      z-index: 1000;
    }

    .win-control-btn {
      width: 46px;
      height: 52px; /* 适配新的高度 */
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #5f6368; /* Chrome风格的图标颜色 */
      cursor: pointer;
      transition: background-color 0.15s ease;
    }

    .win-control-btn:hover {
      background-color: #f1f3f4; /* Chrome风格的悬停背景 */
    }

    #win-close-btn:hover {
      background-color: #ea4335; /* Chrome风格的关闭按钮悬停色 */
      color: white;
    }
  
    .shop-info-icon {
      margin-right: 10px;
      color: #5f6368;
      font-size: 18px;
    }
  
    /* 店铺容器样式 - Chrome标签页容器 */
    #shops-container {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: visible;
      align-items: flex-end; /* 标签页底部对齐 */
      height: 100%;
      padding-left: 115px !important; /* 为左侧控制按钮留出空间 */
      padding-right: 20px !important; /* 为右侧弧线留出空间 */
      -webkit-app-region: no-drag;
      user-select: none;
    }
  
    /* === Chrome浏览器标签页完全复刻 === */

    /* 店铺标签页基础样式 - 进一步增大尺寸 */
    .shop-item {
      height: 42px; /* 进一步增大标签页高度 */
      padding: 0 24px; /* 增大内边距 */
      background: #f1f3f4; /* Chrome非活动标签页背景色 */
      color: #5f6368; /* Chrome非活动标签页文字颜色 */
      cursor: pointer;
      white-space: nowrap;
      transition: background-color 0.15s ease;
      font-size: 15px; /* 进一步增大字体 */
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      min-width: 140px; /* 进一步增大最小宽度 */
      max-width: 260px; /* 进一步增大最大宽度 */
      border-radius: 12px 12px 0 0; /* 进一步增大圆角 */
      margin-top: 5px; /* 稍微增大顶部间距 */
      z-index: 5;
      border: none;
      outline: none;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-appearance: none;
      appearance: none;
      box-shadow: none;
      overflow: visible;
    }
  
    /* Chrome标签页悬停状态 - 彻底移除矩形框 */
    .shop-item:hover:not(.active) {
      background-color: #e8eaed; /* Chrome悬停背景色 */
      color: #3c4043; /* Chrome悬停文字颜色 */
      outline: 0 !important;
      outline-width: 0 !important;
      outline-style: none !important;
      outline-color: transparent !important;
      outline-offset: 0 !important;
      border: 0 !important;
      border-width: 0 !important;
      border-style: none !important;
      border-color: transparent !important;
      box-shadow: none !important;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      appearance: none !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
    }

    /* Chrome标签页激活状态 */
    .shop-item.active {
      background-color: #ffffff; /* Chrome活动标签页背景色 */
      color: #202124; /* Chrome活动标签页文字颜色 */
      height: 42px; /* 与基础高度一致 */
      z-index: 10;
      font-weight: 500; /* Chrome活动标签页字体粗细 */
      border-bottom: none;
    }

    /* Chrome标签页重叠布局 - 适配更大尺寸 */
    .shop-item:not(:first-child) {
      margin-left: -12px; /* 适配新的弧度尺寸 */
    }

    /* Chrome标签页圆弧伪元素基础设置 - 适配更大尺寸 */
    .shop-item::before,
    .shop-item::after {
      content: '';
      position: absolute;
      bottom: 0;
      width: 12px; /* 进一步增大弧度宽度 */
      height: 12px; /* 进一步增大弧度高度 */
      background: transparent;
      z-index: 1;
      pointer-events: none;
    }
  
    /* Chrome标签页左侧圆弧 - 适配更大尺寸 */
    .shop-item::before {
      left: -12px;
      border-bottom-right-radius: 12px;
      box-shadow: 6px 6px 0 6px #f1f3f4; /* 非活动标签页弧线颜色 */
    }

    /* Chrome标签页右侧圆弧 - 适配更大尺寸 */
    .shop-item::after {
      right: -12px;
      border-bottom-left-radius: 12px;
      box-shadow: -6px 6px 0 6px #f1f3f4; /* 非活动标签页弧线颜色 */
    }

    /* Chrome活动标签页圆弧颜色 - 适配更大尺寸 */
    .shop-item.active::before {
      box-shadow: 6px 6px 0 6px #ffffff; /* 活动标签页左侧弧线 */
    }

    .shop-item.active::after {
      box-shadow: -6px 6px 0 6px #ffffff; /* 活动标签页右侧弧线 */
    }

    /* Chrome悬停标签页圆弧颜色 - 适配更大尺寸 */
    .shop-item:hover:not(.active)::before {
      box-shadow: 6px 6px 0 6px #e8eaed; /* 悬停标签页左侧弧线 */
    }

    .shop-item:hover:not(.active)::after {
      box-shadow: -6px 6px 0 6px #e8eaed; /* 悬停标签页右侧弧线 */
    }

    /* Chrome标签页层级和阴影效果 */
    .shop-item.active {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24); /* Chrome活动标签页阴影 */
    }

    /* 【彻底解决矩形边框问题】- 移除所有可能的焦点轮廓和边框 */
    .shop-item,
    .shop-item:hover,
    .shop-item:active,
    .shop-item:focus,
    .shop-item:focus-visible,
    .shop-item:focus-within,
    .shop-item.active,
    .shop-item.active:hover,
    .shop-item.active:active,
    .shop-item.active:focus,
    .shop-item.active:focus-visible,
    .shop-item.active:focus-within {
      outline: 0 !important;
      outline-width: 0 !important;
      outline-style: none !important;
      outline-color: transparent !important;
      outline-offset: 0 !important;
      border: 0 !important;
      border-width: 0 !important;
      border-style: none !important;
      border-color: transparent !important;
      box-shadow: none !important;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      appearance: none !important;
      -webkit-tap-highlight-color: transparent !important;
      -webkit-touch-callout: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
      user-select: none !important;
    }

    /* 【特殊处理】重新应用活动标签页的阴影，但确保没有边框 */
    .shop-item.active {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;
      outline: 0 !important;
      border: 0 !important;
    }

    /* 【特殊处理】重新应用弧线的box-shadow，但确保没有边框 */
    .shop-item::before {
      box-shadow: 6px 6px 0 6px #f1f3f4 !important;
      outline: 0 !important;
      border: 0 !important;
      -webkit-appearance: none !important;
      appearance: none !important;
    }

    .shop-item::after {
      box-shadow: -6px 6px 0 6px #f1f3f4 !important;
      outline: 0 !important;
      border: 0 !important;
      -webkit-appearance: none !important;
      appearance: none !important;
    }

    .shop-item.active::before {
      box-shadow: 6px 6px 0 6px #ffffff !important;
      outline: 0 !important;
      border: 0 !important;
      -webkit-appearance: none !important;
      appearance: none !important;
    }

    .shop-item.active::after {
      box-shadow: -6px 6px 0 6px #ffffff !important;
      outline: 0 !important;
      border: 0 !important;
      -webkit-appearance: none !important;
      appearance: none !important;
    }

    /* 【强力修复悬停弧线】确保悬停时弧线正常但无矩形框 */
    .shop-item:hover:not(.active)::before {
      box-shadow: 6px 6px 0 6px #e8eaed !important;
      outline: 0 !important;
      border: 0 !important;
      -webkit-appearance: none !important;
      appearance: none !important;
      outline-width: 0 !important;
      outline-style: none !important;
      outline-color: transparent !important;
      border-width: 0 !important;
      border-style: none !important;
      border-color: transparent !important;
    }

    .shop-item:hover:not(.active)::after {
      box-shadow: -6px 6px 0 6px #e8eaed !important;
      outline: 0 !important;
      border: 0 !important;
      -webkit-appearance: none !important;
      appearance: none !important;
      outline-width: 0 !important;
      outline-style: none !important;
      outline-color: transparent !important;
      border-width: 0 !important;
      border-style: none !important;
      border-color: transparent !important;
    }

    /* 隐藏滚动条但保持滚动功能 */
    #shops-container::-webkit-scrollbar {
      display: none;
    }

    #shops-container {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }



    /* 标签页内容容器 */
    .shop-item-content {
      flex: 1;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      /* 【修复点击选中bug】确保内容不可选择 */
      user-select: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
      pointer-events: none; /* 防止内容接收点击事件 */
    }
  

  
    /* 主内容区 */
    .main-container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
  
    /* 侧边栏 */
    .sidebar {
      width: 100px; /* 恢复原来的宽度 */
      background: #ffffff; /* 改为白色背景 */
      color: #333;
      display: flex;
      flex-direction: column;
      box-shadow: 2px 0 15px rgba(0, 0, 0, 0.05);
      z-index: 50;
      align-items: center; /* 菜单放置中间 */
      transition: none; /* 移除过渡效果，立即切换 */
      position: relative; /* 添加相对定位，用于展开/收起按钮 */
    }
  
    /* 收起状态的侧边栏 */
    .sidebar.collapsed {
      width: 40px; /* 收起后的宽度 */
      transition: none; /* 确保没有过渡效果 */
      overflow: visible; /* 确保展开按钮可见 */
    }
  
    /* 收起状态下隐藏菜单文字，无过渡动画 */
    .sidebar.collapsed .menu-item span:not(.collapse-text) {
      display: none !important; /* 强制隐藏 */
      transition: none !important; /* 强制没有过渡效果 */
      opacity: 0 !important; /* 确保不可见 */
    }

    /* 收起状态下隐藏菜单箭头 */
    .sidebar.collapsed .menu-arrow {
      display: none !important;
    }

    /* 收起状态下的二级菜单样式 - 显示在一级菜单下方 */
    .sidebar.collapsed .submenu {
      display: none; /* 默认隐藏 */
      position: relative;
      width: 100%;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 4px;
      margin: 3px 0;
      padding: 3px 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-height: none; /* 移除高度限制 */
      overflow: visible; /* 确保内容可见 */
    }

    /* 收起状态下显示的二级菜单 */
    .sidebar.collapsed .submenu.collapsed-show {
      display: flex !important;
      flex-direction: column;
      align-items: center;
    }

    /* 收起状态下的二级菜单项样式 */
    .sidebar.collapsed .submenu .menu-item {
      width: 32px;
      height: 32px;
      margin: 2px auto;
      padding: 3px;
      border-radius: 4px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 9px;
      display: flex !important; /* 强制显示 */
      flex-shrink: 0; /* 防止被压缩 */
    }

    /* 收起状态下的二级菜单项图标 */
    .sidebar.collapsed .submenu .icon-placeholder {
      width: 16px;
      height: 16px;
      margin-right: 0;
      margin-bottom: 2px;
      flex-shrink: 0; /* 防止图标被压缩 */
    }

    /* 收起状态下的二级菜单项文字 */
    .sidebar.collapsed .submenu .menu-item span {
      display: block !important;
      opacity: 1 !important;
      font-size: 8px;
      margin-top: 0;
      text-align: center;
      line-height: 1.1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 30px;
    }

    /* 收起状态下的二级菜单项悬停效果 */
    .sidebar.collapsed .submenu .menu-item:hover {
      background: rgba(0, 0, 0, 0.05);
    }

    /* 收起状态下的二级菜单项激活状态 */
    .sidebar.collapsed .submenu .menu-item.active {
      background: #e3f2fd;
      color: #1976d2;
    }

    /* 收起状态下的菜单容器布局 */
    .sidebar.collapsed .menu-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      overflow-y: auto; /* 允许垂直滚动 */
      padding: 10px 0;
    }

    /* 收起状态下主菜单区域居中显示，支持动态调整 */
    .sidebar.collapsed .main-menu-area {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      flex: 1;
      min-height: 0; /* 允许内容溢出时自动调整 */
    }

    /* 收起状态下的菜单项居中 */
    .sidebar.collapsed .menu-item {
      width: 32px;
      height: 32px;
      margin: 8px 0;
      padding: 4px;
      justify-content: center;
      align-items: center;
    }

    .sidebar.collapsed .bottom-menu-area {
      display: none; /* 收起后隐藏设置按钮 */
    }
  

  
    /* 确保收起状态下的侧边栏不会隐藏展开按钮 */
    .sidebar.collapsed {
      overflow: visible; /* 允许内容溢出，确保按钮可见 */
    }
  
    /* 收起状态下图标大小，无过渡动画 */
    .sidebar.collapsed .icon-placeholder {
      width: 20px;
      height: 20px;
      margin-bottom: 2px;
      transition: none;
    }
  
    /* 展开/收起按钮 - 放在菜单栏模块的右侧 */
    .sidebar-toggle {
      position: absolute !important; /* 使用absolute定位，相对于父元素 */
      bottom: 15px !important; /* 放在底部 */
      left: 100px !important; /* 放在右侧边缘，与侧边栏宽度一致 */
      width: 24px !important;
      height: 24px !important;
      background: #f0f0f0 !important;
      border-radius: 4px 0 0 4px !important; /* 只有左边有圆角 */
      box-shadow: -1px 0 4px rgba(0, 0, 0, 0.15) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      cursor: pointer !important;
      z-index: 9999 !important; /* 确保在最上层 */
      border: 1px solid #dee2e6 !important;
      border-right: none !important; /* 去掉右边框 */
      visibility: visible !important; /* 确保可见 */
      opacity: 1 !important; /* 确保不透明 */
    }
  
    /* 收起状态下的展开按钮位置 - 放在菜单栏模块的左下角中间位置 */
    .sidebar.collapsed .sidebar-toggle {
      position: absolute !important; /* 使用absolute定位，相对于父元素 */
      bottom: 15px !important;
      left: 8px !important; /* 放在收起的侧边栏内部中间位置 */
      transform: none !important;
      z-index: 9999 !important; /* 确保在最上层 */
      background: #f5f5f5 !important; /* 更好看的背景色 */
      border-radius: 4px !important; /* 所有边都有圆角 */
      border: 1px solid #dee2e6 !important; /* 恢复所有边框 */
      width: 24px !important;
      height: 24px !important;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15) !important; /* 更好看的阴影 */
      display: flex !important; /* 强制显示 */
      visibility: visible !important; /* 确保可见 */
      opacity: 1 !important; /* 确保不透明 */
    }
  
    .sidebar-toggle:hover {
        background: #e9ecef;
    }
  
    /* 展开/收起按钮图标 - 使用更美观的三角形 */
    .sidebar-toggle-icon {
      font-size: 12px;
      transition: transform 0.3s ease;
      transform: rotate(0deg); /* 向左的箭头，因为在展开状态下按钮位于右侧，应指向左侧 */
      color: #333; /* 更深的颜色 */
      display: flex;
      align-items: center;
      justify-content: center;
    }
  
    /* 收起状态下的展开按钮图标 */
    .sidebar.collapsed .sidebar-toggle-icon {
      transform: rotate(180deg); /* 向右的箭头，因为在收起状态下按钮位于左侧，应指向右侧 */
    }
  
    /* 替换文本三角形为更好看的HTML三角形符号 */
    .sidebar-toggle-icon:before {
      content: "◀"; /* 使用清晰的箭头符号 */
      font-size: 12px; /* 稍微增大字号 */
      line-height: 1;
      font-weight: 500; /* 使箭头更明显 */
    }
  
    /* 删除"展开菜单"文字 */
    .collapse-text {
      display: none;
    }
  
    .sidebar-top {
      padding: 25px 0;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      width: 100%;
    }
  
    .sidebar-menu {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      overflow-y: auto; /* 添加垂直滚动功能 */
      overflow-x: hidden;
      padding: 20px 0; /* 上下添加间距 */
      position: relative;
    }

    /* 菜单容器，用于居中显示主要菜单项 */
    .menu-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      min-height: 100%;
      position: relative;
    }

    /* 主菜单区域 */
    .main-menu-area {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      width: 100%;
      min-height: 0;
    }

    /* 底部菜单区域 - 保持原来的大小，放在左下角 */
    .bottom-menu-area {
      margin-top: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      padding-top: 10px;
      padding-bottom: 10px;
    }

    /* 分割线样式 */
    .menu-divider {
      width: 80%;
      height: 1px;
      background-color: #e0e0e0;
      margin: 10px 0;
    }

    /* 收起状态下隐藏分割线 */
    .sidebar.collapsed .menu-divider {
      display: none;
    }
  
    /* 确保菜单项文字不可被选中 */
    .menu-item {
      padding: 10px 5px; /* 减小上下内边距 */
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease; /* 只对背景、变换和阴影添加过渡 */
      width: 70%; /* 进一步减小宽度比例 */
      margin: 6px 0; /* 减少菜单项间距 */
      border-radius: 6px;
      flex-direction: column; /* 图标和文字垂直排列 */
      user-select: none; /* 禁止文本选择 */
    }
  
    /* 菜单项文字不需要过渡动画，并进一步缩小字体 */
    .menu-item span {
      transition: none !important; /* 强制禁用任何过渡动画 */
      font-size: 12px; /* 进一步缩小字体 */
      margin-top: 3px; /* 减少与图标的间距 */
      user-select: none; /* 禁止文本选择 */
    }
  
    /* 确保所有菜单项内容没有过渡动画 */
    .menu-item * {
      transition: none !important;
    }
  
    .menu-item:hover {
      background: rgba(0, 0, 0, 0.05);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }
  
    .menu-item.active {
      background: #f0f0f0;
      color: #1a73e8;
    }
  
    .menu-icon {
      margin-bottom: 8px; /* 增加图标和文字之间的间距 */
      font-size: 22px;
      text-align: center;
      color: #555;
    }
  
    .menu-item.active .menu-icon {
      color: #1a73e8;
    }

    /* 一级菜单父项样式 */
    .menu-parent {
      position: relative;
    }

    /* 菜单箭头样式 */
    .menu-arrow {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 10px;
      color: #666;
      transition: transform 0.2s ease;
    }

    /* 展开状态的箭头 */
    .menu-parent.expanded .menu-arrow {
      transform: translateY(-50%) rotate(180deg);
    }

    /* 二级菜单容器 */
    .submenu {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
      background: rgba(0, 0, 0, 0.02);
      width: 100%;
    }

    /* 展开状态的二级菜单 */
    .submenu.expanded {
      max-height: 400px; /* 增加高度以容纳更多菜单项 */
    }

    /* 自定义滚动条样式 */
    .sidebar-menu::-webkit-scrollbar {
      width: 4px;
    }

    .sidebar-menu::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 2px;
    }

    .sidebar-menu::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }

    .sidebar-menu::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.3);
    }

    /* 收起状态下隐藏滚动条 */
    .sidebar.collapsed .sidebar-menu::-webkit-scrollbar {
      display: none;
    }

    /* 二级菜单项样式 */
    .submenu-item {
      width: 85% !important; /* 比一级菜单稍窄 */
      margin: 4px auto !important; /* 减少间距 */
      padding: 8px 5px !important; /* 减少内边距 */
      font-size: 11px !important; /* 稍小的字体 */
      background: rgba(255, 255, 255, 0.8);
      border-left: 3px solid transparent;
    }

    /* 二级菜单项激活状态 */
    .submenu-item.active {
      border-left-color: #1a73e8;
      background: rgba(26, 115, 232, 0.1);
    }

    /* 二级菜单项图标 */
    .submenu-item .icon-placeholder {
      width: 20px !important;
      height: 20px !important;
    }

    /* 二级菜单项文字 */
    .submenu-item span {
      font-size: 11px !important;
      margin-top: 2px !important;
    }
  

  
    /* 用户协议链接样式 */
    .agreement-link {
      font-size: 10px;
      color: #1a73e8;
      margin-top: 5px;
      cursor: pointer;
      text-decoration: underline;
    }



    /* 用户协议弹窗样式 - 与登录页面一致 */
    .agreement-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.6);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      backdrop-filter: blur(2px);
    }

    .agreement-content {
      background-color: white;
      width: 700px;
      height: 600px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      padding: 0;
      overflow: hidden;
      animation: modalFadeIn 0.3s ease-out;
    }

    @keyframes modalFadeIn {
      from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }

    .agreement-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 25px 15px 25px;
      border-bottom: 2px solid #f0f0f0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .agreement-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
    }

    .agreement-close {
      cursor: pointer;
      font-size: 24px;
      color: #95a5a6;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .agreement-close:hover {
      background-color: #e74c3c;
      color: white;
      transform: scale(1.1);
    }

    .agreement-text {
      padding: 25px;
      height: calc(600px - 120px);
      overflow-y: auto;
      font-size: 14px;
      line-height: 1.8;
      color: #2c3e50;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    /* 协议内容样式优化 */
    .agreement-text h1 {
      font-size: 18px;
      font-weight: 600;
      color: #2980b9;
      margin: 25px 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #3498db;
    }

    .agreement-text h2 {
      font-size: 16px;
      font-weight: 600;
      color: #34495e;
      margin: 20px 0 12px 0;
    }

    .agreement-text h3 {
      font-size: 15px;
      font-weight: 500;
      color: #34495e;
      margin: 15px 0 10px 0;
    }

    .agreement-text p {
      margin: 12px 0;
      text-align: justify;
    }

    .agreement-text ul, .agreement-text ol {
      margin: 12px 0;
      padding-left: 25px;
    }

    .agreement-text li {
      margin: 8px 0;
    }

    /* 滚动条样式 */
    .agreement-text::-webkit-scrollbar {
      width: 8px;
    }

    .agreement-text::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    .agreement-text::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    .agreement-text::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }



    /* 加载动画样式 */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid #ddd;
      border-top-color: #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }


  
    /* 内容区 */
    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      height: 100%;
    }
  
    /* 浏览器导航栏标签页 - 缩小到三分之二效果 */
    .tabs {
      display: flex;
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      overflow-x: auto;
      white-space: nowrap;
      padding-left: 0;
      min-height: 32px; /* 缩小到三分之二效果 */
      max-height: 32px; /* 限制最大高度 */
      -webkit-app-region: drag;
    }
  
    .tab {
      padding: 8px 14px; /* 缩小内边距，适配三分之二效果 */
      display: flex;
      align-items: center;
      cursor: pointer;
      border-right: 1px solid #e9ecef;
      background: #f1f3f5;
      min-width: 120px; /* 缩小最小宽度 */
      max-width: 180px; /* 缩小最大宽度 */
      position: relative;
      transition: all 0.2s ease;
      margin-right: 1px;
      -webkit-app-region: no-drag;
    }
  
    .tab.active {
      background: white;
      border-bottom: 2px solid #1a73e8; /* 缩小底部边框 */
      padding-bottom: 6px; /* 适配缩小的内边距 */
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
    }
  
    .tab:hover:not(.active) {
      background: #e9ecef;
    }
  
    .tab-title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 13px; /* 缩小字体，适配三分之二效果 */
      font-weight: 500;
    }
  
    /* 标签页关闭按钮 - 缩小尺寸 */
    .tab-close {
      margin-left: 8px; /* 缩小间距 */
      width: 16px; /* 缩小尺寸 */
      height: 16px; /* 缩小尺寸 */
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px; /* 缩小字体 */
      color: #6c757d;
      transition: all 0.2s ease;
    }
  
    .tab-close:hover {
      background: #dee2e6;
      color: #343a40;
      transform: scale(1.1);
    }
  
    .new-tab {
      padding: 8px 12px; /* 缩小内边距 */
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 16px; /* 缩小字体 */
      color: #6c757d;
      transition: all 0.2s ease;
      background: transparent;
    }
  
    .new-tab:hover {
      background: #e9ecef;
      color: #1a73e8;
    }
  
    /* 浏览器视图 */
    .browser-view {
      flex: 1;
      display: none;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
  
    .browser-view.active {
      display: flex;
    }
  
        /* 内嵌webview样式 */
    .embedded-webview {
      width: 100%;
      height: 100%;
      border: none;
      background: white;
      flex: 1;
      display: flex;
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    /* 浏览器容器 */
    .browser-container {
      flex: 1;
      position: relative;
      overflow: hidden;
      display: flex;
      width: 100%;
      height: 100%;
      background-image: url('../assets/images/背景图.jpg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }
  
    /* 确保整个应用容器填充窗口 */
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }
  
    /* 确保主容器填充整个窗口 */
    .main-container {
      height: 100%;
      display: flex;
      overflow: hidden;
    }
  
    /* 内容区自适应 */
    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      height: 100%;
    }
  
    /* 添加webview缩放样式 - 优化视频号助手页面自适应 */
    @media screen {
      .embedded-webview {
        width: 100%;
        height: 100%;
        transform-origin: top left;
      }
    
      /* 店铺客服、上架产品和视频号助手页面特殊处理，修复右侧空白问题 */
      .embedded-webview[src*="store.weixin.qq.com/shop/kf"],
      .embedded-webview[src*="filehelper.weixin.qq.com"],
      .embedded-webview[src*="channels.weixin.qq.com"] {
        width: 100%;
        height: 100%;
        transform: none;
      }
    
      /* 专门针对视频号助手页面的额外样式，解决右侧空白模块问题 */
      .embedded-webview[src*="channels.weixin.qq.com"] {
        overflow: auto;
        display: flex;
        flex-direction: column;
      }
    }
  
    /* 确保浏览器视图正确显示 */
    .browser-view {
      flex: 1;
      display: none;
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: relative;
    }
  
    .browser-view.active {
      display: flex;
    }
  
    /* 确保标签页容器不占用太多空间 */
    .tabs {
      flex-shrink: 0;
    }
  
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #6c757d;
      background: #f8f9fa;
    }
  
    .empty-icon {
      font-size: 64px;
      margin-bottom: 20px;
      color: #adb5bd;
    }
  
    .empty-text {
      font-size: 18px;
      margin-bottom: 10px;
    }
  
    .empty-subtext {
      font-size: 14px;
      color: #adb5bd;
    }
  
    /* 设置面板 */
    .settings-panel {
      padding: 30px;
      height: 100%;
      overflow-y: auto;
      background: #f8f9fa;
      display: none;
    }

    .settings-panel.active {
      display: flex;
      flex-direction: column;
    }
  
    /* 设置面板上部分（左右布局） */
    .settings-top-container {
      display: flex;
      margin-bottom: 30px;
      gap: 30px;
      align-items: stretch; /* 确保两个模块高度一致 */
    }

    /* 设置面板左右两侧 */
    .settings-left, .settings-right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
    }

    /* 右侧店铺列表区域特殊处理 */
    .settings-right {
      align-items: stretch; /* 确保子元素拉伸 */
      justify-content: flex-start; /* 内容从顶部开始 */
    }

    .settings-left .settings-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .settings-right .settings-section {
      flex: 0 1 auto; /* 改为自适应高度，不强制拉伸 */
      display: flex;
      flex-direction: column;
      justify-content: flex-start; /* 内容从顶部开始排列 */
    }
  


    /* 卡密信息主体框样式 */
    .settings-card-container {
      margin-bottom: 30px;
    }

    .settings-info-card {
      background: white;
      border-radius: 16px;
      padding: 30px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #e9ecef;
      transition: box-shadow 0.2s ease, transform 0.2s ease;
    }

    /* 精确禁用点击效果 - 不破坏布局 */
    .settings-info-card:active,
    .settings-info-card:focus {
      outline: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    .settings-info-row {
      display: flex;
      gap: 30px;
      align-items: stretch;
      flex-wrap: wrap;
      position: relative;
    }

    .settings-info-item {
      flex: 1;
      min-width: 200px;
      text-align: center;
      position: relative;
    }

    /* 竖分割线样式 */
    .settings-info-item:not(:last-child)::after {
      content: '';
      position: absolute;
      right: -15px;
      top: 20px;
      bottom: 20px;
      width: 1px;
      background-color: #dee2e6;
    }

    .settings-info-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: #495057;
      margin-bottom: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .settings-info-value {
      font-size: 16px;
      color: #2c3e50;
      font-weight: 500;
      word-break: break-all;
      margin-bottom: 15px;
      min-height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .license-text {
      font-family: 'Courier New', monospace;
      font-size: 14px;
      color: #2c3e50;
    }

    .copy-license-btn {
      padding: 8px 16px;
      font-size: 12px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .copy-license-btn:hover {
      background: #0056b3;
    }

    /* 退出登录按钮容器 */
    .settings-logout-container {
      text-align: center;
      margin-top: 20px;
      margin-bottom: 30px;
    }

    /* 设置模块背景框样式 */
    .settings-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #e9ecef;
      transition: box-shadow 0.2s ease, transform 0.2s ease;
    }

    /* 精确禁用设置区块点击效果 */
    .settings-section:active,
    .settings-section:focus {
      outline: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }





    /* 设置标题样式优化 */
    .settings-title {
      margin: 0 0 20px 0;
      color: #2c3e50;
      font-size: 20px;
      font-weight: 600;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 12px;
    }

    /* 卡密信息样式优化 */
    .card-info {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 16px;
      border-left: 4px solid #007bff;
    }

    .card-info h3 {
      margin: 0 0 12px 0;
      color: #495057;
      font-size: 16px;
      font-weight: 600;
    }

    .card-info p {
      margin: 8px 0;
      color: #6c757d;
      font-size: 14px;
      line-height: 1.5;
    }

    /* 店铺列表样式优化 - 横向显示 */
    .shop-list {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
      align-items: flex-start;
    }

    /* 店铺列表容器优化 */
    #shop-list-container {
      display: flex;
      flex-direction: column;
      gap: 0;
      flex: 0 1 auto; /* 确保容器不会被拉伸 */
    }

    .shop-list-item {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;
      flex: 1;
      min-width: 200px;
      max-width: 280px;
      text-align: center;
      margin-bottom: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      min-height: 80px;
    }

    .shop-list-item:hover {
      background: #e9ecef;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* 精确禁用店铺列表项点击效果 */
    .shop-list-item:active,
    .shop-list-item:focus {
      outline: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    .shop-list-name {
      font-weight: 600;
      color: #2c3e50;
      font-size: 16px;
      margin-bottom: 8px;
      word-break: break-all;
      text-align: center;
    }

    .shop-list-id {
      color: #6c757d;
      font-size: 13px;
      word-break: break-all;
      text-align: center;
    }

    /* 更多店铺按钮样式 */
    .shop-more-button {
      background: #f8f9fa;
      color: #495057;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 80px;
      text-align: center;
    }

    .shop-more-button:hover {
      background: #e9ecef;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* 精确禁用更多店铺按钮点击效果 */
    .shop-more-button:active,
    .shop-more-button:focus {
      outline: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    /* 精确禁用设置面板中按钮的点击高亮效果 */
    .settings-panel button:active,
    .settings-panel button:focus,
    .settings-panel .settings-btn:active,
    .settings-panel .settings-btn:focus {
      outline: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    /* 全局禁用点击时的蓝色高亮效果，但保持所有其他样式 */
    .settings-panel *:active {
      -webkit-tap-highlight-color: transparent !important;
      outline: none !important;
    }

    /* 特别处理文本和输入框，避免影响功能 */
    .settings-panel input:active,
    .settings-panel input:focus,
    .settings-panel textarea:active,
    .settings-panel textarea:focus {
      outline: 1px solid #007bff !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    /* 关于模块缩小居中样式 */
    .settings-section.about-section {
      max-width: 600px;
      margin: 20px auto;
      padding: 20px;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #e9ecef;
    }

    .settings-section.about-section .settings-section-title {
      text-align: center;
      font-size: 18px;
      margin-bottom: 20px;
      color: #343a40;
    }

    .settings-section.about-section .settings-item {
      margin-bottom: 15px;
    }

    .settings-section.about-section .settings-label {
      font-size: 14px;
      color: #495057;
    }

    .settings-section.about-section .settings-value {
      font-size: 14px;
      color: #6c757d;
    }

    /* 关于部分样式优化 */
    .about-section {
      text-align: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 16px;
      padding: 32px;
      margin-top: 30px;
    }

    .about-section h2 {
      color: white;
      margin-bottom: 16px;
      font-size: 24px;
      font-weight: 700;
    }

    .about-section p {
      margin: 8px 0;
      opacity: 0.9;
      font-size: 16px;
      line-height: 1.6;
    }
  
    .settings-section {
      margin-bottom: 30px;
    }
  
    .settings-title {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #343a40;
      border-bottom: 2px solid #f1f3f5;
      padding-bottom: 10px;
    }
  
    .settings-item {
      margin-bottom: 20px;
    }
  
    .settings-label {
      font-weight: 600;
      margin-bottom: 8px;
      display: block;
    }
  
    .settings-value {
      padding: 10px;
      background: #f8f9fa;
      border-radius: 5px;
      border: 1px solid #dee2e6;
    }
  
    .settings-btn {
      padding: 10px 20px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 5px;
      font-weight: bold;
      cursor: pointer;
    }



    .settings-btn.danger {
      background: #dc3545;
    }
  
    .shop-list {
      list-style: none;
    }
  
    .shop-list-item {
      padding: 10px;
      background: #f8f9fa;
      border-radius: 5px;
      border: 1px solid #dee2e6;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  
    .shop-list-name {
      font-weight: 600;
    }
  
    .shop-list-id {
      font-size: 12px;
      color: #6c757d;
    }
  
    .shop-list-btn {
      padding: 5px 10px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }
  
    /* 动画 */
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  
    @keyframes slideIn {
      from { transform: translateY(20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
  
    /* 工具图标 */
    .icon {
      font-family: "Material Icons";
      font-weight: normal;
      font-style: normal;
      font-size: 24px;
      display: inline-block;
      line-height: 1;
      text-transform: none;
      letter-spacing: normal;
      word-wrap: normal;
      white-space: nowrap;
      direction: ltr;
      -webkit-font-smoothing: antialiased;
      text-rendering: optimizeLegibility;
      -moz-osx-font-smoothing: grayscale;
      font-feature-settings: 'liga';
    }
  </style>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <!-- 预加载Material Icons字体，避免图标加载时显示英文 -->
  <style>
    /* 预加载Material Icons字体 */
    @font-face {
      font-family: 'Material Icons';
      font-style: normal;
      font-weight: 400;
      src: url(https://fonts.gstatic.com/s/materialicons/v140/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
      font-display: block; /* 确保字体在加载前不显示文本 */
    }
  
    /* 确保图标在加载前不可见，加载后立即显示 */
    .menu-icon.icon {
      opacity: 0;
      transition: opacity 0.1s;
      display: none; /* 隐藏Material图标，改用本地图片 */
    }
  
    .fonts-loaded .menu-icon.icon {
      opacity: 1;
      display: none; /* 隐藏Material图标，改用本地图片 */
    }
  
    /* 添加图标预加载占位符 */
    .menu-item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  
    .icon-placeholder {
      width: 24px; /* 缩小图标尺寸 */
      height: 24px; /* 缩小图标尺寸 */
      position: relative; /* 修改为相对定位 */
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      margin: 0 auto 6px; /* 减少底部间距 */
      display: block;
    }
  
    /* 使用本地图片替换SVG图标 */
    .customer-service-icon {
      background-image: url('../assets/icons/店铺客服.png');
      background-size: contain;
      width: 24px; /* 缩小图标尺寸 */
      height: 24px; /* 缩小图标尺寸 */
      position: relative;
      display: block;
      margin: 0 auto 6px; /* 减少底部间距 */
    }
  
    .product-upload-icon {
      background-image: url('../assets/icons/上架产品.png');
      background-size: contain;
      width: 24px; /* 缩小图标尺寸 */
      height: 24px; /* 缩小图标尺寸 */
      position: relative;
      display: block;
      margin: 0 auto 6px; /* 减少底部间距 */
    }
  
    .ai-knowledge-icon {
      background-image: url('../assets/icons/ai知识库.png');
      background-size: contain;
      width: 24px; /* 缩小图标尺寸 */
      height: 24px; /* 缩小图标尺寸 */
      position: relative;
      display: block;
      margin: 0 auto 6px; /* 减少底部间距 */
    }
  
    .video-channels-icon {
      background-image: url('../assets/icons/视频号助手.png');
      background-size: contain;
      width: 24px; /* 缩小图标尺寸 */
      height: 24px; /* 缩小图标尺寸 */
      position: relative;
      display: block;
      margin: 0 auto 6px; /* 减少底部间距 */
    }
  
    .settings-icon {
      background-image: url('../assets/icons/设置.png');
      background-size: contain;
      width: 24px; /* 缩小图标尺寸 */
      height: 24px; /* 缩小图标尺寸 */
      position: relative;
      display: block;
      margin: 0 auto 6px; /* 减少底部间距 */
    }

    .wechat-store-icon {
      background-image: url('../assets/icons/微信小店.png');
      background-size: contain;
      width: 24px; /* 调整为与AI智能客服图标一致的大小 */
      height: 24px; /* 调整为与AI智能客服图标一致的大小 */
      position: relative;
      display: block;
      margin: 0 auto 6px;
    }

    .douyin-store-icon {
      background-image: url('../assets/icons/抖店logo.png');
      background-size: contain;
      width: 24px; /* 调整为与AI智能客服图标一致的大小 */
      height: 24px; /* 调整为与AI智能客服图标一致的大小 */
      position: relative;
      display: block;
      margin: 0 auto 6px;
    }

    /* 店铺列表更多按钮样式 */
    .shop-more-button {
      border: 2px dashed #007bff !important;
      background: #f8f9ff !important;
      transition: all 0.3s ease;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .shop-more-button:hover {
      background: #e6f3ff !important;
      border-color: #0056b3 !important;
    }

    /* Chrome风格切换店铺按钮 */
    .shop-switcher-btn {
      position: absolute;
      left: 75px;
      top: 50%;
      transform: translateY(-50%);
      width: 28px;
      height: 28px;
      background: #f1f3f4; /* Chrome风格背景色 */
      border: 1px solid #dadce0; /* Chrome风格边框 */
      border-radius: 4px; /* Chrome风格圆角 */
      cursor: pointer;
      display: none;
      align-items: center;
      justify-content: center;
      color: #5f6368; /* Chrome风格图标颜色 */
      font-size: 14px;
      transition: all 0.15s ease;
      z-index: 200;
      -webkit-app-region: no-drag;
      box-shadow: none;
    }

    .shop-switcher-btn:hover {
      background: #e8eaed; /* Chrome悬停背景色 */
      border-color: #c4c7ca;
      transform: translateY(-50%);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    }

    .shop-switcher-btn:active {
      background: #dadce0;
      transform: translateY(-50%);
    }

    /* 店铺切换弹窗样式 */
    .shop-switcher-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      -webkit-app-region: no-drag;
    }

    .shop-switcher-modal.show {
      display: flex;
    }

    .shop-switcher-content {
      background: white;
      border-radius: 16px;
      padding: 24px;
      max-width: 500px;
      width: 90%;
      max-height: 80%;
      overflow-y: auto;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      -webkit-app-region: no-drag;
    }

    .shop-switcher-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 2px solid #f0f0f0;
    }

    .shop-switcher-title {
      font-size: 20px;
      font-weight: bold;
      color: #2c3e50;
      margin: 0;
    }

    .shop-switcher-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.3s ease;
    }

    .shop-switcher-close:hover {
      background: #f0f0f0;
      color: #333;
    }

    .shop-switcher-list {
      display: grid;
      gap: 12px;
    }

    .shop-switcher-item {
      padding: 16px;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f8f9fa;
    }

    .shop-switcher-item:hover {
      border-color: #007bff;
      background: #e3f2fd;
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
    }

    .shop-switcher-item.current {
      border-color: #28a745;
      background: #d4edda;
      color: #155724;
    }

    .shop-switcher-item.current:hover {
      border-color: #28a745;
      background: #c3e6cb;
    }

    .shop-item-info {
      flex: 1;
    }

    .shop-item-name {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .shop-item-id {
      font-size: 14px;
      color: #6c757d;
    }

    .shop-item-badge {
      background: #28a745;
      color: white;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <!-- 顶部导航栏 - 移到body的最上方 -->
  <div class="shop-info-bar" id="shop-info-bar">
    <!-- macOS使用系统原生红绿灯按钮，不需要自定义按钮 -->

    <!-- Windows窗口控制按钮 -->
    <div class="win-controls" id="win-controls">
      <div class="win-control-btn" id="win-minimize-btn">&#x2212;</div>
      <div class="win-control-btn" id="win-maximize-btn">&#x25A1;</div>
      <div class="win-control-btn" id="win-close-btn">&#x2715;</div>
    </div>

    <!-- 切换店铺按钮 -->
    <button class="shop-switcher-btn" id="shop-switcher-btn" title="切换店铺">
      ⇄
    </button>



    <div id="shops-container">
      <!-- 店铺列表将在这里动态生成 -->
    </div>
  </div>

  <div class="main-container">
    <div class="sidebar" id="sidebar">
      <div class="sidebar-menu">
        <div class="menu-container">
          <!-- 主菜单区域 -->
          <div class="main-menu-area">
            <!-- 微信小店一级菜单 -->
            <div class="menu-item menu-parent" data-action="wechat-store">
              <div class="icon-placeholder wechat-store-icon"></div>
              <span>微信小店</span>
              <div class="menu-arrow">▼</div>
            </div>

            <!-- 微信小店二级菜单 -->
            <div class="submenu" id="wechat-store-submenu">
              <div class="menu-item submenu-item active" data-action="customer-service">
                <div class="icon-placeholder customer-service-icon"></div>
                <span>AI智能客服</span>
              </div>

              <div class="menu-item submenu-item" data-action="product-upload">
                <div class="icon-placeholder product-upload-icon"></div>
                <span>AI智能上架</span>
              </div>

              <div class="menu-item submenu-item" data-action="video-channels">
                <div class="icon-placeholder video-channels-icon"></div>
                <span>视频号助手</span>
              </div>

              <div class="menu-item submenu-item" data-action="ai-knowledge">
                <div class="icon-placeholder ai-knowledge-icon"></div>
                <span>AI知识库</span>
              </div>
            </div>

            <!-- 抖店一级菜单 -->
            <div class="menu-item menu-parent" data-action="douyin-store">
              <div class="icon-placeholder douyin-store-icon"></div>
              <span>抖店</span>
              <div class="menu-arrow">▼</div>
            </div>

            <!-- 抖店二级菜单（暂时为空，可以后续添加功能） -->
            <div class="submenu" id="douyin-store-submenu">
              <!-- 可以在这里添加抖店相关的功能菜单 -->
            </div>
          </div>

          <!-- 底部菜单区域 -->
          <div class="bottom-menu-area">
            <!-- 分割线 -->
            <div class="menu-divider"></div>
            <!-- 设置菜单保持在底部 -->
            <div class="menu-item" data-action="settings">
              <div class="icon-placeholder settings-icon"></div>
              <span>设置</span>
            </div>
          </div>
        </div>
      </div>
    

    
      <!-- 展开/收起按钮 - 放在侧边栏里面，便于控制位置 -->
      <div class="sidebar-toggle" id="sidebar-toggle">
        <span class="sidebar-toggle-icon"></span>
      </div>
    </div>
  
    <div class="content">
      <div class="tabs" id="tabs">
        <!-- 移除新标签页按钮 -->
      </div>
    
      <div class="browser-container" id="browser-container">
        <!-- 删除欢迎提示模块 -->
      </div>
    
      <div class="settings-panel" id="settings-panel">
        <!-- 第一排：卡密信息主体框 -->
        <div class="settings-section">
          <h2 class="settings-title">卡密信息</h2>
          <div class="settings-card-container">
            <div class="settings-info-card">
              <div class="settings-info-row">
                <div class="settings-info-item">
                  <label class="settings-info-label">卡密</label>
                  <div class="settings-info-value" id="settings-license">
                    <span class="license-text">加载中...</span>
                  </div>
                  <button class="settings-btn copy-license-btn" id="copy-license-btn">复制卡密</button>
                </div>

                <div class="settings-info-item">
                  <label class="settings-info-label">有效期至</label>
                  <div class="settings-info-value" id="settings-expire">加载中...</div>
                </div>

                <div class="settings-info-item">
                  <label class="settings-info-label">功能类型</label>
                  <div class="settings-info-value" id="settings-function">加载中...</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 退出登录按钮 - 与模块框居中对齐 -->
          <div class="settings-logout-container">
            <button class="settings-btn danger" id="logout-btn">退出登录</button>
          </div>
        </div>

        <!-- 第二排：店铺信息 -->
        <div class="settings-section">
          <h2 class="settings-title" id="shop-list-title">卡密绑定的店铺</h2>
          <div id="shop-list-container">
            <ul class="shop-list" id="shop-list">
              <li class="shop-list-item">
                <div>
                  <div class="shop-list-name">加载中...</div>
                </div>
              </li>
            </ul>
            <!-- 老板您好提示框 -->
            <div id="boss-greeting" style="margin-top: 20px; padding: 20px; background: linear-gradient(135deg, #e8f5e8, #f0f8ff); border-radius: 12px; text-align: center; border: 1px solid #d4edda; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
              <p style="margin: 0 0 12px 0; font-weight: bold; color: #155724; font-size: 16px;">老板您好！</p>
              <p style="margin: 0 0 8px 0; color: #0c5460; font-size: 14px;">卡密可以绑定多个店铺同时使用</p>
              <p style="margin: 0; color: #0c5460; font-size: 14px;">如有多店需求请联系代理商增加店铺</p>
            </div>
          </div>
        </div>
      
        <!-- 第三排：关于信息 -->
        <div class="settings-section about-section">
          <h3 class="settings-section-title">关于小梅花AI智能客服</h3>

          <div class="settings-item">
            <label class="settings-label">软件版本</label>
            <div class="settings-value">v1.0.6</div>
          </div>

          <div class="settings-item">
            <label class="settings-label">系统架构</label>
            <div class="settings-value" id="architecture-info">检测中...</div>
          </div>

          <div class="settings-item">
            <label class="settings-label">版权信息</label>
            <div class="settings-value">© 2025 小梅花AI科技</div>
          </div>

          <div class="settings-item">
            <label class="settings-label">用户协议</label>
            <div class="settings-value">
              <div id="agreements-container" style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
                <!-- 协议链接将动态加载到这里 -->
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <!-- 店铺切换弹窗 -->
  <div class="shop-switcher-modal" id="shop-switcher-modal">
    <div class="shop-switcher-content">
      <div class="shop-switcher-header">
        <h3 class="shop-switcher-title">选择店铺</h3>
        <button class="shop-switcher-close" id="shop-switcher-close">×</button>
      </div>
      <div class="shop-switcher-list" id="shop-switcher-list">
        <!-- 店铺列表将在这里动态生成 -->
      </div>
    </div>
  </div>

  <!-- 用户协议弹窗 -->
  <div class="agreement-modal" id="agreement-modal">
    <div class="agreement-content">
      <div class="agreement-header">
        <div class="agreement-title" id="agreement-title">用户协议</div>
        <div class="agreement-close" id="agreement-close">×</div>
      </div>
      <div class="agreement-text" id="agreement-text">
        <!-- 协议内容将通过API动态加载 -->
        <div class="loading-content" style="text-align: center; padding: 40px 20px; color: #666;">
          <div class="loading-spinner" style="display: inline-block; width: 20px; height: 20px; border: 2px solid #ddd; border-top-color: #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 15px;"></div>
          <p>正在加载协议内容...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入版本更新器 -->
  <script src="version-updater.js"></script>

  <script>
    // DOM元素
    const shopSelectorElement = document.getElementById('shop-selector');
    const tabsContainer = document.getElementById('tabs');
    const browserContainer = document.getElementById('browser-container');
    const emptyState = document.getElementById('empty-state');
    const settingsPanel = document.getElementById('settings-panel');
    const settingsLicense = document.getElementById('settings-license');
    const settingsExpire = document.getElementById('settings-expire');
    const settingsFunction = document.getElementById('settings-function');
    // 店铺名称元素已移除
    const settingsShopName = null;
    const logoutBtn = document.getElementById('logout-btn');
    const shopList = document.getElementById('shop-list');
    const menuItems = document.querySelectorAll('.menu-item');
    const shopsContainer = document.getElementById('shops-container');
    const winCloseBtn = document.getElementById('win-close-btn');
    const winMinimizeBtn = document.getElementById('win-minimize-btn');
    const winControls = document.querySelector('.win-controls');
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');

    // 初始化窗口控制按钮显示（根据操作系统）
    function initializeWindowControls() {
      // 检测操作系统
      const isMac = navigator.userAgent.includes('Mac');

      if (isMac) {
        // macOS：使用系统原生红绿灯按钮，隐藏自定义Windows样式按钮
        if (winControls) winControls.style.display = 'none';
      } else {
        // Windows/Linux：显示Windows样式按钮
        if (winControls) winControls.style.display = 'flex';
      }
    }
  
    // 侧边栏展开/收起功能
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => {
        // 移除任何可能的过渡效果
        sidebar.style.transition = 'none';
        Array.from(document.querySelectorAll('.menu-item, .menu-item *')).forEach(el => {
          el.style.transition = 'none';
        });

        // 切换展开状态（修复：使用collapsed类与CSS样式保持一致）
        sidebar.classList.toggle('collapsed');

        // 清理收起状态下的二级菜单显示
        document.querySelectorAll('.submenu').forEach(sub => {
          sub.classList.remove('collapsed-show');
        });

        // 强制重绘以立即应用样式变化，避免过渡效果
        void sidebar.offsetWidth;

        // 【修改】保存当前店铺的侧边栏状态
        if (currentShopId) {
          saveCurrentShopMenuState();
        }
      });
    
      // 【移除全局侧边栏状态加载】现在使用店铺级别的状态管理
    }
  
    // 添加字体加载检测
    document.fonts.ready.then(() => {
      document.body.classList.add('fonts-loaded');
      // 不再需要隐藏占位图标，因为我们直接使用本地图片
      // document.querySelectorAll('.icon-placeholder').forEach(placeholder => {
      //   placeholder.style.display = 'none';
      // });
      // document.querySelectorAll('.menu-icon.icon').forEach(icon => {
      //   icon.style.opacity = '1';
      // });
    });
  
    // 平台检测已在initializeWindowControls()函数中处理，避免重复
  
    // Windows窗口控制按钮事件
    if (winCloseBtn) {
      winCloseBtn.addEventListener('click', () => {
        window.xiaomeihuaAPI.closeWindow();
      });
    }
  
    if (winMinimizeBtn) {
      winMinimizeBtn.addEventListener('click', () => {
        window.xiaomeihuaAPI.minimizeWindow();
      });
    }

    const winMaximizeBtn = document.getElementById('win-maximize-btn');
    if (winMaximizeBtn) {
      winMaximizeBtn.addEventListener('click', () => {
        window.xiaomeihuaAPI.maximizeWindow();
      });

      // 监听窗口最大化状态变化，更新按钮图标
      window.addEventListener('resize', () => {
        // 通过检查窗口尺寸来判断是否最大化
        const isMaximized = window.outerWidth === screen.width && window.outerHeight === screen.height;
        winMaximizeBtn.innerHTML = isMaximized ? '&#x25A2;' : '&#x25A1;';
        winMaximizeBtn.title = isMaximized ? '还原' : '最大化';
      });
    }
  
    // 全局变量
    let shopInfo = null;
    let currentShopId = null;
    let tabs = [];
    let activeTabId = null;
    let shopTabs = {}; // 存储每个店铺的标签页
    let shopMenuStates = {}; // 存储每个店铺的菜单状态

    // 【新增】初始化微信小店状态检测
    function initializeWeixinStoreStatus() {
      console.log('🔍 初始化微信小店状态检测...');

      // 检查微信小店状态
      window.xiaomeihuaAPI.checkWeixinStoreStatus().then(result => {
        if (result.success && result.status) {
          console.log('📊 微信小店状态:', result.status);

          if (result.status.needsClear) {
            console.log(`⚠️ 检测到需要清理微信小店状态: ${result.status.reason}`);

            // 显示状态清理提示（可选）
            showWeixinStoreStatusNotification(result.status.reason);
          } else {
            console.log('✅ 微信小店状态正常');
          }
        }
      }).catch(err => {
        console.error('❌ 检查微信小店状态失败:', err);
      });
    }

    // 【新增】显示微信小店状态通知
    function showWeixinStoreStatusNotification(reason) {
      console.log(`📢 微信小店状态通知: ${reason}`);

      // 这里可以添加用户界面通知，目前只记录日志
      // 可以根据需要添加弹窗或状态栏提示
    }

    // 页面加载完成后立即执行初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        initializeWindowControls();
        initializeWeixinStoreStatus();
      });
    } else {
      initializeWindowControls();
      initializeWeixinStoreStatus();
    }

    // 【新增】微信小店退出登录检测
    let lastLoginState = null; // 记录上次的登录状态
    let loginStateCheckInterval = null;

    // 【新增】检测微信小店登录状态变化
    function detectWeixinLoginStateChange() {
      console.log('🔍 开始检测微信小店登录状态变化...');

      // 每5秒检查一次登录状态
      loginStateCheckInterval = setInterval(() => {
        checkWeixinLoginStateInAllTabs();
      }, 5000);
    }

    // 【新增】检查所有标签页中的微信小店登录状态
    function checkWeixinLoginStateInAllTabs() {
      tabs.forEach(tab => {
        if (tab.viewElement) {
          const webview = tab.viewElement.querySelector('webview');
          if (webview && webview.src && webview.src.includes('store.weixin.qq.com')) {
            checkSingleTabLoginState(tab.id, webview);
          }
        }
      });
    }

    // 【新增】检查单个标签页的登录状态
    function checkSingleTabLoginState(tabId, webview) {
      try {
        // 通过执行JavaScript来检测登录状态
        webview.executeJavaScript(`
          (function() {
            // 检测是否在登录页面
            const isLoginPage = document.title.includes('登录') ||
                               document.title.includes('扫码') ||
                               document.URL.includes('login') ||
                               document.querySelector('.login-qr') !== null ||
                               document.querySelector('[class*="login"]') !== null ||
                               document.querySelector('[class*="qr"]') !== null;

            // 检测是否有登录相关的元素
            const hasLoginElements = document.querySelector('canvas') !== null || // 二维码canvas
                                   document.querySelector('img[src*="qr"]') !== null || // 二维码图片
                                   document.querySelector('.qr-code') !== null; // 二维码容器

            return {
              isLoginPage: isLoginPage,
              hasLoginElements: hasLoginElements,
              title: document.title,
              url: document.URL,
              timestamp: Date.now()
            };
          })();
        `).then(result => {
          handleLoginStateResult(tabId, result);
        }).catch(err => {
          // 忽略执行错误，可能是页面还在加载
        });
      } catch (error) {
        // 忽略检测错误
      }
    }

    // 【新增】处理登录状态检测结果
    function handleLoginStateResult(tabId, result) {
      const currentState = {
        tabId: tabId,
        isLoginPage: result.isLoginPage,
        hasLoginElements: result.hasLoginElements,
        title: result.title,
        url: result.url,
        timestamp: result.timestamp
      };

      // 检查状态变化
      if (lastLoginState && lastLoginState[tabId]) {
        const lastState = lastLoginState[tabId];

        // 检查是否从已登录状态变为登录页面（表示用户退出了登录）
        if (!lastState.isLoginPage && currentState.isLoginPage) {
          console.log('🚪 检测到用户退出登录:', currentState);

          // 通知主进程用户退出了登录
          window.xiaomeihuaAPI.sendPageExitDetected({
            type: 'logout',
            isLogoutExit: true,
            tabId: tabId,
            previousState: lastState,
            currentState: currentState,
            timestamp: Date.now()
          });

          // 【关键修复】立即清理登录状态，确保可以重新扫码登录
          setTimeout(() => {
            console.log('🔄 退出登录后清理状态，确保可以重新扫码登录');

            // 清理本地存储的登录相关状态
            localStorage.removeItem('xmh_recent_login_time');
            localStorage.removeItem('xiaomeihua_login_detected');
            localStorage.removeItem('login_method');
            localStorage.removeItem('session_token');

            // 强制重新初始化页面
            forceReinitializeWeixinStorePage(tabId);
          }, 2000);

          // 【关键修复】额外的完全重置机制
          setTimeout(() => {
            completelyResetWeixinStorePage(tabId);
          }, 5000);
        }

        // 检查是否从登录页面变为已登录状态（表示用户重新登录了）
        if (lastState.isLoginPage && !currentState.isLoginPage && !currentState.title.includes('登录')) {
          console.log('🔑 检测到用户重新登录:', currentState);

          // 通知主进程用户重新登录了
          if (typeof window.xiaomeihuaAPI.triggerShopDetection === 'function') {
            window.xiaomeihuaAPI.triggerShopDetection({
              isNewLogin: true,
              tabId: tabId,
              previousState: lastState,
              currentState: currentState,
              timestamp: Date.now()
            });
          }
        }
      }

      // 更新登录状态记录
      if (!lastLoginState) {
        lastLoginState = {};
      }
      lastLoginState[tabId] = currentState;
    }

    // 启动登录状态检测
    detectWeixinLoginStateChange();

    // 【新增】监听webview重置事件
    if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.onResetAllWebviews) {
      window.xiaomeihuaAPI.onResetAllWebviews(() => {
        console.log('🔄 收到重置所有webview的指令');
        resetAllWeixinStoreWebviews();
      });
    }

    // 【新增】重置所有微信小店webview
    function resetAllWeixinStoreWebviews() {
      console.log('🔄 开始重置所有微信小店webview...');

      tabs.forEach(tab => {
        if (tab.url && (tab.url.includes('store.weixin.qq.com') || tab.url.includes('weixin.qq.com'))) {
          console.log(`🔄 重置微信小店标签页: ${tab.title}`);

          if (tab.viewElement) {
            const webview = tab.viewElement.querySelector('webview');
            if (webview) {
              // 完全移除旧的webview
              webview.remove();
              console.log(`🗑️ 已移除标签页 ${tab.title} 的webview`);

              // 延迟重新创建webview，确保session分区已经重置
              setTimeout(() => {
                console.log(`🔄 重新创建标签页 ${tab.title} 的webview`);
                loadUrl(tab.id, tab.url, true); // 强制重新加载
              }, 3000);
            }
          }
        }
      });

      console.log('✅ 所有微信小店webview重置完成');
    }

    // 【新增】强制重新初始化微信小店页面
    function forceReinitializeWeixinStorePage(tabId) {
      console.log(`🔄 强制重新初始化微信小店页面: ${tabId}`);

      const tab = tabs.find(t => t.id === tabId);
      if (!tab || !tab.viewElement) {
        console.log(`⚠️ 未找到标签页 ${tabId}`);
        return;
      }

      const webview = tab.viewElement.querySelector('webview');
      if (!webview) {
        console.log(`⚠️ 标签页 ${tabId} 没有webview`);
        return;
      }

      // 检查是否是微信小店页面
      if (!tab.url || (!tab.url.includes('store.weixin.qq.com') && !tab.url.includes('weixin.qq.com'))) {
        console.log(`⚠️ 标签页 ${tabId} 不是微信小店页面`);
        return;
      }

      console.log(`🔄 开始重新初始化微信小店页面: ${tab.title}`);

      try {
        // 1. 停止当前页面加载
        webview.stop();

        // 2. 清理webview的导航历史
        if (webview.clearHistory) {
          webview.clearHistory();
        }

        // 3. 强制重新加载到微信小店首页
        const baseUrl = 'https://store.weixin.qq.com/shop/kf';
        const shopId = extractShopIdFromUrl(tab.url);
        const newUrl = shopId ? `${baseUrl}?shop_id=${shopId}` : baseUrl;

        console.log(`🔄 重新加载微信小店页面到: ${newUrl}`);

        // 4. 延迟加载，确保清理完成
        setTimeout(() => {
          webview.src = newUrl;
          tab.url = newUrl; // 更新标签页URL
        }, 1000);

        console.log(`✅ 微信小店页面 ${tab.title} 重新初始化完成`);
      } catch (error) {
        console.error(`❌ 重新初始化微信小店页面失败:`, error);
      }
    }

    // 【新增】从URL中提取店铺ID
    function extractShopIdFromUrl(url) {
      try {
        const urlObj = new URL(url);
        return urlObj.searchParams.get('shop_id');
      } catch (error) {
        console.log('提取店铺ID失败:', error);
        return null;
      }
    }

    // 【关键修复】完全重置微信小店页面
    function completelyResetWeixinStorePage(tabId) {
      console.log(`🔄 完全重置微信小店页面: ${tabId}`);

      const tab = tabs.find(t => t.id === tabId);
      if (!tab || !tab.viewElement) {
        console.log(`⚠️ 未找到标签页 ${tabId}`);
        return;
      }

      // 检查是否是微信小店页面
      if (!tab.url || (!tab.url.includes('store.weixin.qq.com') && !tab.url.includes('weixin.qq.com'))) {
        console.log(`⚠️ 标签页 ${tabId} 不是微信小店页面`);
        return;
      }

      console.log(`🔄 开始完全重置微信小店页面: ${tab.title}`);

      try {
        // 1. 完全移除旧的webview
        const oldWebview = tab.viewElement.querySelector('webview');
        if (oldWebview) {
          oldWebview.remove();
          console.log(`🗑️ 已移除旧的webview`);
        }

        // 2. 等待一段时间确保清理完成
        setTimeout(() => {
          // 3. 创建全新的webview
          const newWebview = document.createElement('webview');
          newWebview.className = 'embedded-webview';

          // 4. 设置webview属性
          const shopId = extractShopIdFromUrl(tab.url);
          const cleanUrl = 'https://store.weixin.qq.com/';

          newWebview.setAttribute('src', cleanUrl);

          // 【关键】使用全新的session分区
          const newPartition = `persist:shop_${shopId}_${Date.now()}`;
          console.log(`🔧 使用全新的session分区: ${newPartition}`);
          newWebview.setAttribute('partition', newPartition);
          newWebview.setAttribute('webpreferences', 'nodeIntegration=no, contextIsolation=yes, allowRunningInsecureContent=yes, sandbox=no');

          // 5. 添加加载事件监听
          newWebview.addEventListener('did-start-loading', () => {
            console.log(`🔄 新webview开始加载: ${cleanUrl}`);
          });

          newWebview.addEventListener('did-finish-load', () => {
            console.log(`✅ 新webview加载完成: ${cleanUrl}`);
          });

          newWebview.addEventListener('did-fail-load', (e) => {
            console.error(`❌ 新webview加载失败:`, e);
          });

          // 6. 添加到视图容器
          tab.viewElement.appendChild(newWebview);

          // 7. 更新标签页URL
          tab.url = cleanUrl;

          console.log(`✅ 微信小店页面 ${tab.title} 完全重置完成`);
        }, 2000);

      } catch (error) {
        console.error(`❌ 完全重置微信小店页面失败:`, error);
      }
    }

    // 【新增】测试函数：模拟退出登录
    window.testLogout = function() {
      console.log('🧪 开始测试退出登录功能...');

      // 找到第一个微信小店标签页
      const weixinTab = tabs.find(tab =>
        tab.url && (tab.url.includes('store.weixin.qq.com') || tab.url.includes('weixin.qq.com'))
      );

      if (!weixinTab) {
        console.log('❌ 未找到微信小店标签页');
        return;
      }

      console.log(`🧪 模拟标签页 ${weixinTab.title} 的退出登录...`);

      // 模拟退出登录状态变化
      const mockLogoutState = {
        tabId: weixinTab.id,
        isLoginPage: true,
        hasLoginElements: true,
        title: '微信小店登录',
        url: weixinTab.url,
        timestamp: Date.now()
      };

      const mockPreviousState = {
        tabId: weixinTab.id,
        isLoginPage: false,
        hasLoginElements: false,
        title: '微信小店',
        url: weixinTab.url,
        timestamp: Date.now() - 10000
      };

      // 更新登录状态记录
      if (!lastLoginState) {
        lastLoginState = {};
      }
      lastLoginState[weixinTab.id] = mockPreviousState;

      // 触发退出登录检测
      handleLoginStateResult(weixinTab.id, mockLogoutState);

      console.log('🧪 退出登录测试完成');
    };

    console.log('🧪 测试函数已加载，可以在控制台中调用 testLogout() 来测试退出登录功能');

    // 【新增】确保应用程序最小化时功能正常运行
    function ensureBackgroundActivity() {
      console.log('🔄 设置后台活动保持...');

      // 重写页面可见性API
      if (typeof document !== 'undefined') {
        Object.defineProperty(document, 'hidden', {
          get: function() { return false; },
          configurable: true
        });

        Object.defineProperty(document, 'visibilityState', {
          get: function() { return 'visible'; },
          configurable: true
        });
      }

      // 确保所有webview在后台保持活跃
      const webviews = document.querySelectorAll('webview');
      webviews.forEach((webview, index) => {
        if (webview && webview.getWebContents) {
          try {
            webview.getWebContents().setBackgroundThrottling(false);
            console.log(`已设置webview ${index} 后台保持活跃`);
          } catch (err) {
            console.warn(`设置webview ${index} 后台活跃失败:`, err);
          }
        }
      });

      // 确保定时器继续运行
      if (loginStateCheckInterval) {
        console.log('登录状态检测定时器正在运行');
      }

      console.log('✅ 后台活动保持设置完成');
    }

    // 【新增】监听窗口重新激活事件
    window.addEventListener('xiaomeihua-reactivate', (event) => {
      console.log('🔄 收到重新激活事件，重新检查所有功能状态...');

      // 重新检查登录状态
      if (typeof detectWeixinLoginStateChange === 'function') {
        detectWeixinLoginStateChange();
      }

      // 重新检查所有webview状态
      const webviews = document.querySelectorAll('webview');
      webviews.forEach((webview, index) => {
        if (webview && webview.src) {
          console.log(`重新检查webview ${index} 状态:`, webview.src);
        }
      });

      console.log('✅ 重新激活检查完成');
    });

    // 【新增】页面加载完成后立即设置后台活动保持
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', ensureBackgroundActivity);
      document.addEventListener('DOMContentLoaded', loadArchitectureInfo);
    } else {
      ensureBackgroundActivity();
      loadArchitectureInfo();
    }

    // 【新增】定期确保后台活动保持（每30秒检查一次）
    setInterval(() => {
      ensureBackgroundActivity();
    }, 30000);

    // 【新增】加载架构信息
    async function loadArchitectureInfo() {
      try {
        if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.getArchitectureInfo) {
          const archInfo = await window.xiaomeihuaAPI.getArchitectureInfo();

          // 更新架构信息显示
          const archElement = document.getElementById('architecture-info');
          if (archElement) {
            archElement.textContent = archInfo.platform.description;
          }

          // 更新更新通道显示
          const channelElement = document.getElementById('update-channel');
          if (channelElement) {
            channelElement.textContent = archInfo.platform.apiDescription;
          }

          console.log('🏗️  架构信息已加载:', archInfo.platform.description);
        }
      } catch (error) {
        console.error('❌ 加载架构信息失败:', error);

        // 显示错误信息
        const archElement = document.getElementById('architecture-info');
        if (archElement) {
          archElement.textContent = '检测失败';
        }

        const channelElement = document.getElementById('update-channel');
        if (channelElement) {
          channelElement.textContent = '未知';
        }
      }
    }

    // 【彻底修复数据互通问题】移除全局AI智能客服标签页跟踪，每个店铺完全独立

    // 保存当前店铺的菜单状态
    function saveCurrentShopMenuState() {
      if (!currentShopId) return;

      const activeMenuItem = document.querySelector('.menu-item.active');
      const sidebarExpanded = sidebar && !sidebar.classList.contains('collapsed');

      shopMenuStates[currentShopId] = {
        activeMenuAction: activeMenuItem ? activeMenuItem.getAttribute('data-action') : 'customer-service',
        sidebarExpanded: sidebarExpanded || false
      };

      console.log(`保存店铺 ${currentShopId} 的菜单状态:`, shopMenuStates[currentShopId]);
    }

    // 恢复店铺的菜单状态
    function restoreShopMenuState(shopId) {
      if (!shopId || !shopMenuStates[shopId]) {
        // 如果没有保存的状态，使用默认状态
        setActiveMenuItem('customer-service');
        if (sidebar && sidebar.classList.contains('collapsed')) {
          sidebar.classList.remove('collapsed');
        }
        return;
      }

      const menuState = shopMenuStates[shopId];
      console.log(`恢复店铺 ${shopId} 的菜单状态:`, menuState);

      // 恢复活动菜单项
      setActiveMenuItem(menuState.activeMenuAction);

      // 恢复侧边栏展开状态
      if (sidebar) {
        if (menuState.sidebarExpanded) {
          sidebar.classList.remove('collapsed');
        } else {
          sidebar.classList.add('collapsed');
        }
      }
    }

    // 设置活动菜单项
    function setActiveMenuItem(action) {
      // 移除所有二级菜单项的活动状态
      document.querySelectorAll('.submenu-item').forEach(item => {
        item.classList.remove('active');
      });

      // 设置指定菜单项为活动状态
      const targetMenuItem = document.querySelector(`[data-action="${action}"]`);
      if (targetMenuItem && targetMenuItem.classList.contains('submenu-item')) {
        targetMenuItem.classList.add('active');

        // 确保父级菜单展开
        const submenu = targetMenuItem.closest('.submenu');
        if (submenu) {
          submenu.classList.add('expanded');
          const parentMenu = submenu.previousElementSibling;
          if (parentMenu && parentMenu.classList.contains('menu-parent')) {
            parentMenu.classList.add('expanded');
          }
        }
      }
    }
  
    // 创建店铺不匹配警告弹窗的公共函数
    function createShopMismatchWarning(selectedShop, currentPageShop, showAutoExit = false, onConfirm = null) {
      // 创建弹窗样式
      const styleId = 'xmh-shop-warning-style';
      if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
          @keyframes fadeInScale {
            0% {
              opacity: 0;
              transform: scale(0.95);
            }
            100% {
              opacity: 1;
              transform: scale(1);
            }
          }

          @keyframes pulse {
            0% {
              opacity: 0.6;
            }
            50% {
              opacity: 1;
            }
            100% {
              opacity: 0.6;
            }
          }

          @keyframes shake {
            0%, 100% {
              transform: translateX(0);
            }
            20%, 60% {
              transform: translateX(-3px);
            }
            40%, 80% {
              transform: translateX(3px);
            }
          }

          @keyframes slideIn {
            0% {
              opacity: 0;
              transform: translateY(20px);
            }
            100% {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .xmh-shop-warning-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            z-index: 9999999;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .xmh-shop-warning-modal {
            animation: fadeInScale 0.4s ease-out forwards;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-radius: 16px;
            background-color: #f8fafc;
            max-width: 600px;
            width: 90%;
            overflow: hidden;
            position: relative;
          }

          .xmh-alert-bar {
            animation: pulse 1.5s infinite ease-in-out;
            background-color: #ef4444;
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            display: flex;
            justify-content: center;
            padding: 18px 32px;
          }

          .xmh-warning-icon {
            animation: shake 0.8s infinite;
            color: white;
            font-size: 28px;
            margin-right: 16px;
          }

          .xmh-alert-title {
            color: white;
            font-weight: 600;
            font-size: 22px;
          }

          .xmh-modal-content {
            padding: 32px;
          }

          .xmh-error-message {
            margin-bottom: 32px;
            text-align: center;
          }

          .xmh-error-title {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 12px;
            font-size: 22px;
          }

          .xmh-error-subtitle {
            color: #6b7280;
            font-size: 18px;
          }

          .xmh-shop-info-container {
            margin-bottom: 32px;
          }

          .xmh-shop-info {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            padding: 24px 28px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
          }

          .xmh-correct-shop {
            border-left: 8px solid #00B42A;
            animation: slideIn 0.5s ease-out 0.2s both;
          }

          .xmh-wrong-shop {
            border-left: 8px solid #F53F3F;
            animation: slideIn 0.5s ease-out 0.4s both;
          }

          .xmh-shop-icon {
            margin-right: 18px;
            font-size: 28px;
          }

          .xmh-shop-text-container {
            display: flex;
            align-items: center;
            line-height: 1.6;
          }

          .xmh-shop-label {
            font-size: 18px;
            margin-right: 12px;
            font-weight: bold;
          }

          .xmh-correct-label {
            color: #00B42A;
          }

          .xmh-wrong-label {
            color: #F53F3F;
          }

          .xmh-shop-name {
            font-weight: bold;
            font-size: 20px;
          }

          .xmh-correct-name {
            color: #00B42A;
          }

          .xmh-wrong-name {
            color: #F53F3F;
          }

          .xmh-auto-exit-text {
            color: #F53F3F;
            font-weight: bold;
            font-size: 19px;
            text-align: center;
            margin-bottom: 32px;
          }

          .xmh-shop-warning-btn {
            transition: all 0.2s ease;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            color: white;
            border: none;
            padding: 16px 32px;
            font-size: 20px;
            border-radius: 12px;
            cursor: pointer;
            width: 100%;
            font-weight: 600;
          }

          .xmh-shop-warning-btn:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
          }
        `;
        document.head.appendChild(style);
      }

      // 检查是否已经显示了警告
      if (document.querySelector('.xmh-shop-warning-overlay')) {
        console.log('警告弹窗已存在，不重复显示');
        return;
      }

      // 创建弹窗
      const overlay = document.createElement('div');
      overlay.className = 'xmh-shop-warning-overlay';

      const modal = document.createElement('div');
      modal.className = 'xmh-shop-warning-modal';

      // 创建顶部警报条
      const alertBar = document.createElement('div');
      alertBar.className = 'xmh-alert-bar';

      const alertContent = document.createElement('div');
      alertContent.style.display = 'flex';
      alertContent.style.alignItems = 'center';

      // 添加警告图标
      const warningIcon = document.createElement('i');
      warningIcon.className = 'fas fa-exclamation-triangle xmh-warning-icon';

      const alertTitle = document.createElement('span');
      alertTitle.className = 'xmh-alert-title';
      alertTitle.textContent = '店铺登录错误提醒';

      alertContent.appendChild(warningIcon);
      alertContent.appendChild(alertTitle);
      alertBar.appendChild(alertContent);
      modal.appendChild(alertBar);

      // 创建内容区域
      const modalContent = document.createElement('div');
      modalContent.className = 'xmh-modal-content';

      // 错误提示文字
      const errorMessage = document.createElement('div');
      errorMessage.className = 'xmh-error-message';

      const errorTitle = document.createElement('p');
      errorTitle.className = 'xmh-error-title';
      errorTitle.textContent = '老板您好！您登录的店铺不正确～';

      const errorSubtitle = document.createElement('p');
      errorSubtitle.className = 'xmh-error-subtitle';
      errorSubtitle.textContent = '请您登录正确的店铺，否则无法使用小梅花AI功能';

      errorMessage.appendChild(errorTitle);
      errorMessage.appendChild(errorSubtitle);
      modalContent.appendChild(errorMessage);

      // 店铺信息容器
      const shopInfoContainer = document.createElement('div');
      shopInfoContainer.className = 'xmh-shop-info-container';

      // 正确店铺信息
      const correctShopInfo = document.createElement('div');
      correctShopInfo.className = 'xmh-shop-info xmh-correct-shop';

      const correctShopContainer = document.createElement('div');
      correctShopContainer.style.display = 'flex';
      correctShopContainer.style.alignItems = 'center';
      correctShopContainer.style.lineHeight = '1.5';

      const correctShopIcon = document.createElement('i');
      correctShopIcon.className = 'fas fa-check-circle xmh-shop-icon';
      correctShopIcon.style.color = '#00B42A';

      const correctShopTextContainer = document.createElement('div');
      correctShopTextContainer.className = 'xmh-shop-text-container';

      const correctShopLabel = document.createElement('span');
      correctShopLabel.className = 'xmh-shop-label xmh-correct-label';
      correctShopLabel.textContent = '正确的店铺:';

      const correctShopName = document.createElement('span');
      correctShopName.className = 'xmh-shop-name xmh-correct-name';
      correctShopName.textContent = selectedShop || "未知";

      correctShopTextContainer.appendChild(correctShopLabel);
      correctShopTextContainer.appendChild(correctShopName);
      correctShopContainer.appendChild(correctShopIcon);
      correctShopContainer.appendChild(correctShopTextContainer);
      correctShopInfo.appendChild(correctShopContainer);
      shopInfoContainer.appendChild(correctShopInfo);

      // 错误店铺信息
      const wrongShopInfo = document.createElement('div');
      wrongShopInfo.className = 'xmh-shop-info xmh-wrong-shop';

      const wrongShopContainer = document.createElement('div');
      wrongShopContainer.style.display = 'flex';
      wrongShopContainer.style.alignItems = 'center';
      wrongShopContainer.style.lineHeight = '1.5';

      const wrongShopIcon = document.createElement('i');
      wrongShopIcon.className = 'fas fa-times-circle xmh-shop-icon';
      wrongShopIcon.style.color = '#F53F3F';

      const wrongShopTextContainer = document.createElement('div');
      wrongShopTextContainer.className = 'xmh-shop-text-container';

      const wrongShopLabel = document.createElement('span');
      wrongShopLabel.className = 'xmh-shop-label xmh-wrong-label';
      wrongShopLabel.textContent = '您登录的店铺:';

      const wrongShopName = document.createElement('span');
      wrongShopName.className = 'xmh-shop-name xmh-wrong-name';
      wrongShopName.textContent = currentPageShop || "未知";

      wrongShopTextContainer.appendChild(wrongShopLabel);
      wrongShopTextContainer.appendChild(wrongShopName);
      wrongShopContainer.appendChild(wrongShopIcon);
      wrongShopContainer.appendChild(wrongShopTextContainer);
      wrongShopInfo.appendChild(wrongShopContainer);
      shopInfoContainer.appendChild(wrongShopInfo);

      modalContent.appendChild(shopInfoContainer);

      // 如果需要显示自动退出提示
      if (showAutoExit) {
        const autoExitText = document.createElement('div');
        autoExitText.className = 'xmh-auto-exit-text';
        autoExitText.textContent = '系统会为您自动退出，请您重新扫码登录正确店铺';
        modalContent.appendChild(autoExitText);
      }

      const btn = document.createElement('button');
      btn.className = 'xmh-shop-warning-btn';
      btn.textContent = '确认';
      btn.onclick = function() {
        overlay.remove();
        if (onConfirm) {
          onConfirm();
        }
      };

      modalContent.appendChild(btn);
      modal.appendChild(modalContent);
      overlay.appendChild(modal);
      document.body.appendChild(overlay);
    }

    // 检查是否有店铺不匹配记录并显示弹窗
    function checkShopMismatchRecord() {
      try {
        const mismatchData = localStorage.getItem('xmh_shop_mismatch');
        if (mismatchData) {
          const data = JSON.parse(mismatchData);
          console.log('检测到店铺不匹配记录:', data);

          createShopMismatchWarning(
            data.selectedShop,
            data.currentPageShop,
            false,
            function() {
              localStorage.removeItem('xmh_shop_mismatch');
            }
          );
        }
      } catch (error) {
        console.error('检查店铺不匹配记录失败:', error);
      }
    }
    
    // 初始化
    async function init() {
      try {
        console.log('开始初始化...');

        // 清除所有检测状态，确保软件重启后重新检测
        clearShopCheckStatus();
        console.log('软件启动：已清除所有店铺检测状态');

        // 检查是否有店铺不匹配记录
        checkShopMismatchRecord();
      
        // 获取店铺信息
        if (!shopInfo || Object.keys(shopInfo).length === 0) {
          console.log('获取店铺信息...');
        shopInfo = await window.xiaomeihuaAPI.getShopInfo();
          console.log('获取到店铺信息:', shopInfo);
        }
      
        // 店铺名称显示已移除
        // if (settingsShopName) {
        //   settingsShopName.textContent = shopInfo.shopName || '未知店铺';
        // } else {
        //   console.error('未找到settingsShopName元素');
        // }
      
        // 设置卡密信息
        if (settingsLicense) {
          const licenseKey = shopInfo.license || await window.xiaomeihuaAPI.getLicenseKey();
          const licenseTextElement = settingsLicense.querySelector('.license-text');

          if (licenseKey) {
            // 显示完整卡密，不再加密
            if (licenseTextElement) {
              licenseTextElement.textContent = licenseKey;
            }
          } else {
            if (licenseTextElement) {
              licenseTextElement.textContent = '未知';
            }
          }
        } else {
          console.error('未找到settingsLicense元素');
        }
      
        // 设置到期日期
        if (settingsExpire) {
          if (shopInfo.expireDate) {
            const expireDate = new Date(shopInfo.expireDate);
            const now = new Date();
            const diffTime = expireDate.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
            let statusColor = '#28a745'; // 绿色
            let statusText = '';
          
            if (diffDays <= 0) {
              statusColor = '#dc3545'; // 红色
              statusText = '(已过期)';
            } else if (diffDays <= 7) {
              statusColor = '#ffc107'; // 黄色
              statusText = `(剩余${diffDays}天)`;
            } else {
              statusText = `(剩余${diffDays}天)`;
            }
          
            settingsExpire.innerHTML = `
              <div>${formatDate(expireDate)}</div>
              <small style="color: ${statusColor}; font-weight: bold;">${statusText}</small>
            `;
          } else {
            settingsExpire.textContent = '未知';
          }
        } else {
          console.error('未找到settingsExpire元素');
        }
      
        // 设置功能类型
        if (settingsFunction) {
          let functionText = '';
          if (shopInfo.hasCustomerService && shopInfo.hasProductListing) {
            functionText = '小梅花AI全功能版本';
          } else if (shopInfo.hasCustomerService) {
            functionText = '小梅花AI客服功能版本';
          } else if (shopInfo.hasProductListing) {
            functionText = '小梅花AI智能上架功能版本';
          } else {
            functionText = '无功能版本';
          }
          settingsFunction.textContent = functionText;
        } else {
          console.error('未找到settingsFunction元素');
        }
      
        // 设置当前店铺ID
        currentShopId = shopInfo.shopId;
      
        // 处理店铺列表
        let shops = [];
        if (shopInfo.shops && shopInfo.shops.length > 0) {
          shops = shopInfo.shops;
        } else {
          // 单店铺情况
          shops = [{
            id: shopInfo.shopId,
            name: shopInfo.shopName,
            wechatStoreId: shopInfo.wechatStoreId || '未知'
          }];
        }
      
        console.log('更新店铺列表:', shops);
      
        // 更新顶部导航栏的店铺列表
        if (shopsContainer) {
          updateTopShopsList(shops);
        } else {
          console.error('未找到shopsContainer元素');
        }
      
        // 同时更新设置中的店铺列表（但不显示切换按钮）
        if (shopList) {
          updateShopList(shops);
        
          // 更新店铺列表标题，显示单店或多店
          const shopListTitle = document.getElementById('shop-list-title');
          if (shopListTitle) {
            if (shops.length > 1) {
              shopListTitle.textContent = `卡密绑定的店铺（多店）`;
            } else {
              shopListTitle.textContent = `卡密绑定的店铺（单店）`;
            }
          }
        } else {
          console.error('未找到shopList元素');
        }
      
        // 【彻底修复数据互通问题】移除全局唯一性检查，每个店铺完全独立
        console.log('初始化完成，每个店铺的标签页完全独立'); // 延迟1秒执行，确保所有标签页都已加载完成

        // 【新增】确保所有现有标签页都有正确的店铺标识
        ensureTabsHaveShopId();

        // 【新增】根据店铺数量决定是否显示切换店铺按钮
        updateShopSwitcherVisibility(shops.length);

        // 【新增】初始化切换店铺按钮
        initShopSwitcher();

        // 【新增】默认隐藏浏览器导航栏
        hideBrowserTabs();

        console.log('初始化完成');
      } catch (error) {
        console.error('初始化错误:', error);

        // 尝试恢复基本功能，但不自动打开店铺客服页面
          console.log('尝试恢复基本功能...');
      }
    }
  
    // 格式化日期
    function formatDate(date) {
      if (!date) return '未知';
    
      try {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }).replace(/\//g, '-');
      } catch (e) {
        return date.toLocaleString();
      }
    }
  
    // 更新顶部导航栏的店铺列表
    function updateTopShopsList(shops) {
      shopsContainer.innerHTML = '';
    
      shops.forEach(shop => {
        const shopItem = document.createElement('div');
        shopItem.className = 'shop-item';
        if (shop.id === currentShopId) {
          shopItem.classList.add('active');
        }
      
        // 创建标签页内容容器
        const shopItemContent = document.createElement('span');
        shopItemContent.className = 'shop-item-content';
        shopItemContent.textContent = shop.name || '未知店铺';
        shopItem.appendChild(shopItemContent);
      
        shopItem.setAttribute('data-shop-id', shop.id);
        shopItem.setAttribute('data-shop-name', shop.name);
      
        // 【修复】点击店铺切换，添加防抖机制和彻底移除焦点效果
        let shopSwitchTimeout = null;
        shopItem.addEventListener('click', function(e) {
          // 【彻底解决矩形边框问题】阻止默认行为和焦点
          e.preventDefault();
          e.stopPropagation();

          // 【强制移除焦点】确保元素不会获得焦点
          this.blur();
          if (document.activeElement === this) {
            document.activeElement.blur();
          }

          // 【强制移除所有可能的样式】
          this.style.outline = '0';
          this.style.border = '0';
          this.style.boxShadow = 'none';

          const shopId = this.getAttribute('data-shop-id');
          const shopName = this.getAttribute('data-shop-name');

          // 【新增】检查是否在特殊页面（设置或AI知识库）
          if (isInSpecialPage()) {
            console.log('在特殊页面中点击店铺名称，切换到店铺网页显示');
            handleSpecialPageShopClick(shopId, shopName);
            return;
          }

          // 【关键修复】在菜单栏点击时立即设置切换状态，防止误检测
          console.log('🔄 菜单栏店铺点击，立即设置切换状态防止误检测');
          setShopSwitchingProtection();

          // 【优化】移除防抖延迟，立即切换店铺以消除卡顿
          if (shopSwitchTimeout) {
            clearTimeout(shopSwitchTimeout);
          }

          // 【立即执行】直接切换店铺，不使用setTimeout
          switchShop(shopId, shopName);

          // 更新激活状态
          document.querySelectorAll('.shop-item').forEach(item => {
            item.classList.remove('active');
            // 【同时移除所有店铺标签页的焦点效果】
            item.style.outline = '0';
            item.style.border = '0';
            item.blur();
          });
          this.classList.add('active');
          // 【确保激活的标签页也没有焦点效果】
          this.style.outline = '0';
          this.style.border = '0';
          this.blur();

          // 【修复】移除自动创建AI智能客服标签页的逻辑
          // 店铺切换时不应该自动打开任何网页，让用户主动选择需要的功能
        });

        // 【彻底解决悬停矩形框问题】添加鼠标悬停事件监听
        shopItem.addEventListener('mouseenter', function(e) {
          // 【强制移除悬停时的所有可能样式】
          this.style.outline = '0';
          this.style.border = '0';
          this.style.boxShadow = 'none';
          this.blur();
          if (document.activeElement === this) {
            document.activeElement.blur();
          }
        });

        shopItem.addEventListener('mouseover', function(e) {
          // 【强制移除鼠标移入时的所有可能样式】
          this.style.outline = '0';
          this.style.border = '0';
          this.style.boxShadow = 'none';
          this.blur();
        });

        shopItem.addEventListener('mouseleave', function(e) {
          // 【强制移除鼠标移出时的所有可能样式】
          this.style.outline = '0';
          this.style.border = '0';
          this.style.boxShadow = 'none';
          this.blur();
        });
      
        shopsContainer.appendChild(shopItem);
      });
    }

    // 【新增】更新顶部店铺标签页的激活状态
    function updateTopShopsActiveState(activeShopId) {
      console.log(`更新顶部店铺标签页激活状态，激活店铺ID: ${activeShopId}`);

      // 移除所有店铺的激活状态
      document.querySelectorAll('.shop-item').forEach(item => {
        item.classList.remove('active');
      });

      // 为当前店铺添加激活状态
      const activeShopItem = document.querySelector(`.shop-item[data-shop-id="${activeShopId}"]`);
      if (activeShopItem) {
        activeShopItem.classList.add('active');
        console.log(`成功激活店铺标签页: ${activeShopItem.getAttribute('data-shop-name')}`);
      } else {
        console.warn(`未找到店铺ID为 ${activeShopId} 的标签页元素`);
      }
    }

    // 【新增】更新浏览器标题栏，确保导航栏同步显示
    function updateBrowserTitle(shopName) {
      try {
        const newTitle = `小梅花客服助手 - ${shopName}`;
        document.title = newTitle;
        console.log(`浏览器标题已更新: ${newTitle}`);

        // 通知主进程更新窗口标题
        if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.updateWindowTitle) {
          window.xiaomeihuaAPI.updateWindowTitle(newTitle);
        }
      } catch (error) {
        console.error('更新浏览器标题失败:', error);
      }
    }

    // 【新增】自动滚动到激活的店铺位置
    function scrollToActiveShop(activeShopId) {
      console.log(`自动滚动到店铺: ${activeShopId}`);

      const activeShopItem = document.querySelector(`.shop-item[data-shop-id="${activeShopId}"]`);
      const shopsContainer = document.getElementById('shops-container');

      if (!activeShopItem || !shopsContainer) {
        console.warn('未找到店铺元素或容器，无法滚动');
        return;
      }

      // 延迟执行滚动，确保DOM更新完成
      setTimeout(() => {
        // 计算店铺元素相对于容器的位置
        const shopOffsetLeft = activeShopItem.offsetLeft;
        const shopWidth = activeShopItem.offsetWidth;
        const containerWidth = shopsContainer.offsetWidth;
        const currentScrollLeft = shopsContainer.scrollLeft;

        // 检查店铺是否已经在可视区域内
        const shopVisibleLeft = shopOffsetLeft - currentScrollLeft;
        const shopVisibleRight = shopVisibleLeft + shopWidth;

        // 如果店铺不在可视区域内，则滚动到合适位置
        if (shopVisibleLeft < 0 || shopVisibleRight > containerWidth) {
          // 计算目标滚动位置，让店铺居中显示
          const targetScrollLeft = shopOffsetLeft - (containerWidth / 2) + (shopWidth / 2);

          // 确保滚动位置不超出边界
          const maxScrollLeft = shopsContainer.scrollWidth - containerWidth;
          const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));

          // 平滑滚动到目标位置
          shopsContainer.scrollTo({
            left: finalScrollLeft,
            behavior: 'smooth'
          });

          console.log(`滚动到位置: ${finalScrollLeft}, 店铺位置: ${shopOffsetLeft}`);
        } else {
          console.log('店铺已在可视区域内，无需滚动');
        }
      }, 100); // 延迟100ms确保DOM更新完成
    }

    // 更新设置中的店铺列表
    function updateShopList(shops) {
      shopList.innerHTML = '';

      // 【优化】最多显示3个店铺，超过3个显示"更多"按钮
      const maxDisplayShops = 3;
      const shopsToShow = shops.slice(0, maxDisplayShops);
      const hasMoreShops = shops.length > maxDisplayShops;

      shopsToShow.forEach((shop, index) => {
        const li = document.createElement('li');
        li.className = 'shop-list-item';

        li.innerHTML = `
          <div>
            <div class="shop-list-name">${shop.name || '未知店铺'}</div>
            <div class="shop-list-id">ID: ${shop.wechatStoreId || shop.id || '未知'}</div>
          </div>
        `;

        shopList.appendChild(li);
      });

      // 如果有更多店铺，添加"更多"按钮
      if (hasMoreShops) {
        const moreButton = document.createElement('li');
        moreButton.className = 'shop-list-item shop-more-button';
        moreButton.innerHTML = `
          <div style="text-align: center; cursor: pointer; color: #495057; font-weight: bold;">
            <div style="font-size: 16px;">查看更多店铺 (总共${shops.length}个)</div>
          </div>
        `;

        // 添加点击事件
        moreButton.addEventListener('click', () => {
          showAllShopsModal(shops);
        });

        shopList.appendChild(moreButton);
      }

      // 确保"老板您好"提示框显示在店铺列表（包括更多按钮）之后
      const bossGreeting = document.getElementById('boss-greeting');
      if (bossGreeting) {
        bossGreeting.style.display = 'block';
        bossGreeting.style.order = '999'; // 确保在最后显示
      }
    }
  
    // 【全局变量】店铺切换状态标志
    let isShopSwitching = false;

    // 【新增】确保所有标签页都有正确的店铺标识
    function ensureTabsHaveShopId() {
      if (!currentShopId) return;

      tabs.forEach(tab => {
        if (!tab.shopId) {
          tab.shopId = currentShopId;
        }

        // 确保DOM元素也有店铺标识
        if (tab.element && !tab.element.getAttribute('data-shop-id')) {
          tab.element.setAttribute('data-shop-id', tab.shopId);
        }
        if (tab.viewElement && !tab.viewElement.getAttribute('data-shop-id')) {
          tab.viewElement.setAttribute('data-shop-id', tab.shopId);
        }

        // 确保webview也有店铺标识
        const webview = tab.viewElement.querySelector('webview');
        if (webview && !webview.getAttribute('data-shop-id')) {
          webview.setAttribute('data-shop-id', tab.shopId);
        }
      });
    }

    // 【新增】将指定标签页移动到第一位
    function moveTabToFirst(tabId) {
      const tabIndex = tabs.findIndex(t => t.id === tabId);
      if (tabIndex > 0) { // 如果不是第一个
        const tab = tabs[tabIndex];

        // 从数组中移除
        tabs.splice(tabIndex, 1);
        // 插入到第一位
        tabs.unshift(tab);

        // 在DOM中也移动到第一位
        const firstTab = tabsContainer.querySelector('.tab');
        if (firstTab && firstTab !== tab.element) {
          tabsContainer.insertBefore(tab.element, firstTab);
        }

        console.log(`标签页 ${tab.title} 已移动到第一位`);

        // 更新当前店铺的标签页状态
        if (currentShopId) {
          shopTabs[currentShopId] = {
            tabs: [...tabs],
            activeTabId: activeTabId
          };
        }
      }
    }

    // 【新增】根据店铺数量控制切换店铺按钮的显示
    function updateShopSwitcherVisibility(shopCount) {
      const switcherBtn = document.getElementById('shop-switcher-btn');
      if (!switcherBtn) {
        console.log('切换店铺按钮元素未找到');
        return;
      }

      if (shopCount > 6) {
        switcherBtn.style.display = 'flex';
        switcherBtn.style.alignItems = 'center';
        switcherBtn.style.justifyContent = 'center';
        console.log(`店铺数量为 ${shopCount}，显示切换店铺按钮`);
      } else {
        switcherBtn.style.display = 'none';
        console.log(`店铺数量为 ${shopCount}，隐藏切换店铺按钮`);
      }
    }

    // 【新增】初始化切换店铺按钮
    function initShopSwitcher() {
      const switcherBtn = document.getElementById('shop-switcher-btn');
      const switcherModal = document.getElementById('shop-switcher-modal');
      const switcherClose = document.getElementById('shop-switcher-close');

      if (!switcherBtn) {
        console.log('切换店铺按钮元素未找到');
        return;
      }

      if (!switcherModal) {
        console.log('切换店铺弹窗元素未找到');
        return;
      }

      console.log('初始化切换店铺按钮成功');

      // 点击切换按钮显示弹窗
      switcherBtn.addEventListener('click', (e) => {
        console.log('切换店铺按钮被点击');
        e.preventDefault();
        e.stopPropagation();
        showShopSwitcherModal();
      });

      // 点击关闭按钮隐藏弹窗
      if (switcherClose) {
        switcherClose.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          hideShopSwitcherModal();
        });
      }

      // 点击弹窗背景隐藏弹窗
      switcherModal.addEventListener('click', (e) => {
        if (e.target === switcherModal) {
          hideShopSwitcherModal();
        }
      });
    }

    // 【新增】显示店铺切换弹窗
    function showShopSwitcherModal() {
      console.log('显示店铺切换弹窗');
      const modal = document.getElementById('shop-switcher-modal');
      if (!modal) {
        console.log('弹窗元素未找到');
        return;
      }

      if (!shopInfo || !shopInfo.shops) {
        console.log('店铺信息未加载或无店铺数据');
        alert('店铺信息未加载，请稍后再试');
        return;
      }

      console.log('更新并显示弹窗，店铺数量:', shopInfo.shops.length);

      // 更新弹窗内容
      updateShopSwitcherModal();

      // 显示弹窗
      modal.classList.add('show');
    }

    // 【新增】隐藏店铺切换弹窗
    function hideShopSwitcherModal() {
      console.log('隐藏店铺切换弹窗');
      const modal = document.getElementById('shop-switcher-modal');
      if (!modal) return;

      modal.classList.remove('show');
    }

    // 【新增】更新店铺切换弹窗内容
    function updateShopSwitcherModal() {
      const list = document.getElementById('shop-switcher-list');
      if (!list) {
        console.log('弹窗列表元素未找到');
        return;
      }

      if (!shopInfo || !shopInfo.shops) {
        console.log('店铺信息未加载或无店铺数据');
        list.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">暂无店铺数据</div>';
        return;
      }

      console.log('更新弹窗内容，店铺列表:', shopInfo.shops);
      list.innerHTML = '';

      shopInfo.shops.forEach((shop, index) => {
        console.log(`添加店铺 ${index + 1}:`, shop.name, shop.id);
        const item = document.createElement('div');
        item.className = `shop-switcher-item ${shop.id === currentShopId ? 'current' : ''}`;

        item.innerHTML = `
          <div class="shop-item-info">
            <div class="shop-item-name">${shop.name || '未知店铺'}</div>
            <div class="shop-item-id">ID: ${shop.id}</div>
          </div>
          ${shop.id === currentShopId ? '<div class="shop-item-badge">当前店铺</div>' : ''}
        `;

        // 添加点击事件
        item.addEventListener('click', (e) => {
          console.log('点击店铺:', shop.name, shop.id);
          e.stopPropagation();

          if (shop.id !== currentShopId) {
            console.log('切换到店铺:', shop.name);

            // 【关键修复】在弹窗点击时立即设置切换状态，防止误检测
            console.log('🔄 弹窗店铺点击，立即设置切换状态防止误检测');
            setShopSwitchingProtection();

            // 隐藏弹窗
            hideShopSwitcherModal();
            // 切换店铺
            switchShop(shop.id, shop.name);
          } else {
            console.log('已经是当前店铺，无需切换');
            // 隐藏弹窗
            hideShopSwitcherModal();
            // 即使是当前店铺，也滚动到其位置以确保可见
            scrollToActiveShop(shop.id);
          }
        });

        list.appendChild(item);
      });

      console.log('弹窗内容更新完成，共添加', shopInfo.shops.length, '个店铺');
    }

    // 【新增】显示所有店铺的弹窗
    function showAllShopsModal(shops) {
      // 创建弹窗背景
      const modalOverlay = document.createElement('div');
      modalOverlay.className = 'modal-overlay';
      modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      `;

      // 创建弹窗内容
      const modalContent = document.createElement('div');
      modalContent.className = 'modal-content';
      modalContent.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 30px;
        max-width: 800px;
        width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        position: relative;
      `;

      // 创建弹窗标题
      const modalHeader = document.createElement('div');
      modalHeader.style.cssText = `
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
      `;

      const modalTitle = document.createElement('h3');
      modalTitle.textContent = `全部绑定店铺 (${shops.length}个)`;
      modalTitle.style.cssText = `
        margin: 0;
        color: #333;
        font-size: 22px;
        font-weight: 600;
      `;

      const closeButton = document.createElement('button');
      closeButton.innerHTML = '×';
      closeButton.style.cssText = `
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #999;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      `;

      modalHeader.appendChild(modalTitle);
      modalHeader.appendChild(closeButton);

      // 创建店铺列表
      const shopsList = document.createElement('div');
      shopsList.style.cssText = `
        max-height: 500px;
        overflow-y: auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
        padding: 10px 0;
      `;

      shops.forEach((shop, index) => {
        const shopItem = document.createElement('div');
        shopItem.style.cssText = `
          padding: 20px;
          border: 2px solid ${shop.id === currentShopId ? '#007bff' : '#e0e0e0'};
          border-radius: 10px;
          background: ${shop.id === currentShopId ? '#f0f8ff' : '#ffffff'};
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          cursor: pointer;
        `;

        shopItem.innerHTML = `
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
            <div style="font-weight: bold; color: #333; font-size: 16px;">
              ${shop.name || '未知店铺'}
            </div>
            ${shop.id === currentShopId ? '<div style="background: #007bff; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">当前店铺</div>' : ''}
          </div>
          <div style="font-size: 14px; color: #666; background: #f8f9fa; padding: 8px; border-radius: 6px;">
            <strong>店铺ID:</strong> ${shop.wechatStoreId || shop.id || '未知'}
          </div>
        `;

        // 添加悬停效果
        shopItem.addEventListener('mouseenter', () => {
          if (shop.id !== currentShopId) {
            shopItem.style.borderColor = '#007bff';
            shopItem.style.boxShadow = '0 4px 16px rgba(0, 123, 255, 0.2)';
          }
        });

        shopItem.addEventListener('mouseleave', () => {
          if (shop.id !== currentShopId) {
            shopItem.style.borderColor = '#e0e0e0';
            shopItem.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
          }
        });

        shopsList.appendChild(shopItem);
      });

      modalContent.appendChild(modalHeader);
      modalContent.appendChild(shopsList);
      modalOverlay.appendChild(modalContent);

      // 添加关闭事件
      const closeModal = () => {
        document.body.removeChild(modalOverlay);
      };

      closeButton.addEventListener('click', closeModal);
      modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
          closeModal();
        }
      });

      // 添加到页面
      document.body.appendChild(modalOverlay);
    }

    // 【彻底重写店铺切换】使用显示/隐藏方式，完全避免DOM操作
    function switchShop(shopId, shopName) {
      console.log(`切换到店铺: ${shopName} (ID: ${shopId})`);

      // 如果是同一个店铺，直接返回
      if (currentShopId === shopId) {
        console.log('切换到相同店铺，无需操作');
        return;
      }

      // 【关键修复】设置店铺切换状态标志，防止任何页面操作和店铺检测
      setShopSwitchingProtection();
      console.log('🔄 店铺切换开始，设置保护标志防止页面刷新和店铺检测');

      // 【新增】保存当前店铺的菜单状态
      if (currentShopId) {
        saveCurrentShopMenuState();
      }

      // 【全新方案】使用CSS显示/隐藏，完全避免DOM操作
      if (currentShopId && tabs.length > 0) {
        console.log(`隐藏当前店铺 ${currentShopId} 的所有标签页，使用CSS隐藏`);

        // 保存当前店铺的标签页状态
        shopTabs[currentShopId] = {
          tabs: [...tabs], // 浅拷贝即可，保持引用
          activeTabId: activeTabId
        };

        // 【关键修复】使用CSS隐藏，不进行任何DOM操作
        tabs.forEach(tab => {
          // 给标签页添加店铺标识
          tab.element.setAttribute('data-shop-id', currentShopId);
          tab.viewElement.setAttribute('data-shop-id', currentShopId);

          // 隐藏标签页
          tab.element.style.display = 'none';
          tab.viewElement.style.display = 'none';

          // 确保webview也被隐藏但保持在DOM中
          const webview = tab.viewElement.querySelector('webview');
          if (webview) {
            webview.style.display = 'none';
            webview.setAttribute('data-shop-id', currentShopId);
          }
        });
      }

      // 更新当前店铺ID
      const previousShopId = currentShopId;
      currentShopId = shopId;
      console.log(`店铺ID已更新: ${previousShopId} -> ${currentShopId}`);

      // 【优化】立即更新顶部店铺标签页的激活状态，确保浏览器导航栏同步
      updateTopShopsActiveState(shopId);

      // 【新增】自动滚动到切换的店铺位置
      scrollToActiveShop(shopId);

      // 【新增】立即更新浏览器标题栏，确保导航栏同步显示
      updateBrowserTitle(shopName);

      // 【全新方案】使用CSS显示方式恢复标签页，避免DOM操作
      console.log(`店铺切换到 ${shopId}，使用CSS显示方式恢复标签页`);

      // 恢复目标店铺的标签页
      if (shopTabs[shopId] && shopTabs[shopId].tabs && shopTabs[shopId].tabs.length > 0) {
        console.log(`恢复店铺 ${shopId} 的标签页，共 ${shopTabs[shopId].tabs.length} 个`);

        // 【全新方案】直接使用保存的标签页引用，通过CSS显示
        tabs = shopTabs[shopId].tabs;

        // 【全新方案】直接显示已保存的标签页，不创建新的DOM元素
        tabs.forEach(tab => {
          // 显示标签页元素
          tab.element.style.display = 'flex';

          // 显示视图元素（但先隐藏，等激活时再显示）
          tab.viewElement.style.display = 'none';

          // 确保webview保持隐藏状态，不触发任何事件
          const webview = tab.viewElement.querySelector('webview');
          if (webview) {
            webview.style.display = 'none';
            console.log(`标签页 ${tab.title} 的webview保持完全静止状态，避免页面刷新`);
          }

          console.log(`显示标签页 ${tab.title}，保持所有页面状态不变`);
        });

        // 【彻底修复页面刷新问题】激活之前激活的标签页时完全禁用自动加载
        if (shopTabs[shopId].activeTabId) {
          activateTab(shopTabs[shopId].activeTabId, false); // 强制禁用自动加载
          console.log(`店铺切换：激活标签页 ${shopTabs[shopId].activeTabId}，完全保持页面静止状态`);
        } else if (tabs.length > 0) {
          activateTab(tabs[0].id, false); // 强制禁用自动加载
          console.log(`店铺切换：激活第一个标签页 ${tabs[0].id}，完全保持页面静止状态`);
        }
      } else {
        console.log(`店铺 ${shopName} (ID: ${shopId}) 没有标签页，等待用户操作`);
        // 【修复】移除自动创建AI智能客服标签页的逻辑
        // 当切换到没有标签页的店铺时，应该保持空白状态，让用户主动选择需要的功能
        tabs = [];
        activeTabId = null;
      }
      
      // 更新设置面板中显示的店铺信息
      updateSettingsPanelForShop(shopId, shopName);

      // 【新增】恢复新店铺的菜单状态
      restoreShopMenuState(shopId);

      // 【优化】延长保护标志时间，确保店铺切换完全完成
      setTimeout(() => {
        clearShopSwitchingProtection();
        console.log('🔄 店铺切换完成，清除保护标志');

        // 【新增】更新店铺切换弹窗内容，确保"当前店铺"标识正确显示
        updateShopSwitcherModal();

        // 【优化】切换店铺后，清除之前店铺的检测状态，允许新店铺重新检测
        if (previousShopId && previousShopId !== shopId) {
          console.log(`切换店铺：清除之前店铺 ${previousShopId} 的检测状态，允许新店铺 ${shopId} 重新检测`);
        }

        console.log('✅ 店铺切换保护期结束，现在可以进行正常的店铺检测');
      }, 3000); // 【关键修复】延长到3秒，确保页面完全稳定后才允许检测
    }
    
    // 更新设置面板中显示的店铺信息
    function updateSettingsPanelForShop(shopId, shopName) {
      if (!shopInfo || !shopInfo.shops) return;

      // 查找当前店铺的详细信息
      const currentShop = shopInfo.shops.find(shop => shop.id === shopId);
      if (!currentShop) return;

      // 店铺名称显示已移除
      // if (settingsShopName) {
      //   settingsShopName.textContent = currentShop.name || shopName || '未知店铺';
      // }

      // 可以在这里添加更多设置面板的更新逻辑
      console.log(`已更新设置面板为店铺: ${currentShop.name || shopName}`);
    }

    // 【彻底修复数据互通问题】移除全局唯一性管理函数，每个店铺完全独立
  
    // 创建标签页
    function createTab(title, url, isActive = true, shopId = null) {
      try {
        console.log(`创建标签页: ${title}, URL: ${url}, 店铺ID: ${shopId || '默认'}`);
      
        // 处理AI智能上架页面的特殊情况
        if (url.includes('merchant/product') || 
            url.includes('上架产品') || url.includes('AI智能上架') || 
            (title && (title.includes('上架产品') || title.includes('AI智能上架')))) {
          console.log('检测到AI智能上架页面链接，重定向到filehelper');
          url = 'https://filehelper.weixin.qq.com/';
          title = 'AI智能上架';
        }
      
        // 处理特殊URL
        if (url.startsWith('javascript:') || url === 'about:blank') {
          console.log('忽略特殊URL:', url);
          return null;
        }
      
        // 确保URL格式正确
        try {
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url.replace(/^\/\//, '');
          }
        
          // 检查是否是有效的URL
          new URL(url);
        } catch (error) {
          console.error('无效的URL:', url, error);
          return null;
        }
      
        // 【彻底修复数据互通问题】移除AI智能客服标签页的全局唯一性检查
        // 每个店铺都可以有自己独立的AI智能客服标签页
        console.log(`创建标签页: ${title}，店铺ID: ${shopId || currentShopId}，确保数据完全隔离`);

        // 检查其他类型的重复标签页
        const existingTab = tabs.find(t => t.url === url);
        if (existingTab) {
          console.log(`已存在相同URL的标签页: ${existingTab.id}，直接激活`);
          activateTab(existingTab.id);
          return existingTab.id;
        }
      
        // 生成唯一ID
        const tabId = 'tab-' + Date.now();
        console.log(`创建新标签页ID: ${tabId}`);
      
        // 判断是否是AI智能客服页面
        const isCustomerService = url.includes('store.weixin.qq.com/shop/kf') || title === 'AI智能客服' || title === '店铺客服';
      
        // 创建标签页元素
        const tabElement = document.createElement('div');
        tabElement.className = 'tab';
        tabElement.id = tabId;
      
        // 只有非店铺客服页面才添加关闭按钮
        if (!isCustomerService) {
          tabElement.innerHTML = `
            <div class="tab-title">${title}</div>
            <div class="tab-close">×</div>
          `;
        
          // 【修复】绑定关闭按钮事件，添加防抖机制
          setTimeout(() => {
            const closeBtn = tabElement.querySelector('.tab-close');
            if (closeBtn) {
              let closeClickTimeout = null;
              closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();

                // 防抖：防止快速重复点击关闭按钮
                if (closeClickTimeout) {
                  clearTimeout(closeClickTimeout);
                }
                closeClickTimeout = setTimeout(() => {
                  removeTab(tabId);
                }, 100); // 100ms防抖延迟，稍长一些防止误操作
              });
            }
          }, 0);
        } else {
          // 店铺客服页面没有关闭按钮
          tabElement.innerHTML = `
            <div class="tab-title">${title}</div>
          `;
        }
      
        // 【优化】AI智能客服标签页始终排在第一个
        if (isCustomerService) {
          // AI智能客服标签页插入到第一个位置
          const firstTab = tabsContainer.querySelector('.tab');
          if (firstTab) {
            tabsContainer.insertBefore(tabElement, firstTab);
          } else {
            tabsContainer.appendChild(tabElement);
          }
          console.log('AI智能客服标签页已排在第一位');
        } else {
          // 其他标签页添加到末尾
          tabsContainer.appendChild(tabElement);
        }
      
        // 创建浏览器视图
        const viewElement = document.createElement('div');
        viewElement.className = 'browser-view';
        viewElement.id = `view-${tabId}`;
        browserContainer.appendChild(viewElement);

        // 【关键修复】为新创建的标签页添加店铺标识
        const currentShop = shopId || currentShopId;
        tabElement.setAttribute('data-shop-id', currentShop);
        viewElement.setAttribute('data-shop-id', currentShop);

        // 保存标签信息
        const tab = {
          id: tabId,
          title,
          url,
          element: tabElement,
          viewElement,
          isCustomerService, // 添加是否是店铺客服的标志
          shopId: currentShop // 保存创建该标签页时的店铺ID
        };
      
        // 【优化】AI智能客服标签页在数组中也排在第一个
        if (isCustomerService) {
          tabs.unshift(tab); // 插入到数组开头
          console.log('AI智能客服标签页已插入到tabs数组第一位');
        } else {
          tabs.push(tab); // 添加到数组末尾
        }

        // 【彻底修复数据互通问题】移除全局跟踪逻辑，每个店铺完全独立
        console.log(`标签页 ${tabId} 已添加到店铺 ${shopId || currentShopId}，确保数据隔离`);

        // 更新当前店铺的标签页状态
        if (currentShopId) {
          shopTabs[currentShopId] = {
            tabs: [...tabs],
            activeTabId: tabId
          };
        }
      
        // 【修复】绑定事件，添加防抖机制
        let tabClickTimeout = null;
        tabElement.addEventListener('click', (e) => {
          // 防抖：防止快速重复点击
          if (tabClickTimeout) {
            clearTimeout(tabClickTimeout);
          }
          tabClickTimeout = setTimeout(() => {
            activateTab(tabId);
          }, 50); // 50ms防抖延迟
        });
      
        // 激活新标签页
        activateTab(tabId);
      
        // 加载URL并传递卡密信息
        loadUrl(tabId, url);
      
        console.log(`标签页 ${tabId} 创建成功`);
        return tabId;
      } catch (error) {
        console.error('创建标签页失败:', error);
        return null;
      }
    }
  
    // 激活标签页
    function activateTab(tabId, autoLoad = true) {
      // 【关键修复】如果正在进行店铺切换，强制禁用自动加载
      if (isShopSwitching) {
        autoLoad = false;
        console.log('检测到店铺切换状态，强制禁用自动加载以防止页面刷新');
      }

      // 隐藏设置面板
      settingsPanel.style.display = 'none';

      // 【关键修复】只操作当前店铺的标签页，避免影响其他店铺
      tabs.forEach(tab => {
        // 只处理当前店铺的标签页
        if (tab.shopId === currentShopId) {
          tab.element.classList.remove('active');
          tab.viewElement.classList.remove('active');
          tab.viewElement.style.display = 'none';

          // 确保webview也被隐藏
          const webview = tab.viewElement.querySelector('webview');
          if (webview) {
            webview.style.display = 'none';
          }

          // 检查并更新AI智能客服页面的关闭按钮
          if (tab.isCustomerService || tab.url.includes('store.weixin.qq.com/shop/kf') || tab.title === 'AI智能客服' || tab.title === '店铺客服') {
            // 确保AI智能客服页面没有关闭按钮
            const closeBtn = tab.element.querySelector('.tab-close');
            if (closeBtn) {
              closeBtn.remove();
            }
          }
        }
      });

      // 激活当前标签页
      const tab = tabs.find(t => t.id === tabId);
      if (tab) {
        tab.element.classList.add('active');
        tab.viewElement.classList.add('active');

        // 显示当前标签页的视图元素
        tab.viewElement.style.display = 'flex';

        // 【彻底修复店铺切换页面刷新问题】检查webview是否存在
        const webview = tab.viewElement.querySelector('webview');
        if (webview) {
          // 如果webview存在，直接显示（保持原有状态）
          webview.style.display = 'flex';
          console.log(`激活标签页 ${tab.title}，保持原有页面状态`);

          // 【关键修复】无论是否为店铺切换，都不调用任何可能导致页面刷新的操作
          // 完全移除loadUrl调用和URL获取操作，确保页面状态100%保持不变
          console.log(`标签页 ${tab.title} 完全保持静止状态，不进行任何URL操作`);
        } else {
          // 如果webview不存在，说明这是一个空的标签页
          console.log(`标签页 ${tab.title} 没有webview，autoLoad: ${autoLoad}`);

          // 【关键修复】只有在明确允许自动加载且不是店铺切换的情况下才创建webview
          if (autoLoad && tab.url && tab.url !== 'about:blank' && tab.url !== '') {
            // 只有在允许自动加载且有有效URL的情况下才重新加载
            console.log(`为标签页 ${tab.title} 重新加载URL: ${tab.url}`);
            loadUrl(tabId, tab.url);
          } else {
            // 如果不允许自动加载或没有有效URL，显示空白页面
            console.log(`标签页 ${tab.title} 保持静止状态，不自动加载页面`);
          }
        }

        activeTabId = tabId;

        // 更新当前店铺的标签页状态
        if (currentShopId) {
          shopTabs[currentShopId] = {
            tabs: [...tabs],
            activeTabId
          };
        }

        // 【新增】根据当前激活的标签页类型控制浏览器导航栏显示
        if (tab.title === 'AI知识库' || (tab.url && tab.url.includes('ai-knowledge.html'))) {
          // AI知识库页面隐藏导航栏
          hideBrowserTabs();
          console.log('激活AI知识库页面，隐藏浏览器导航栏');
        } else {
          // 其他页面显示导航栏
          showBrowserTabs();
          console.log('激活普通页面，显示浏览器导航栏');
        }
      }
    }
  
    // 移除标签页
    function removeTab(tabId, silent = false, forceRemove = false) {
      const tabIndex = tabs.findIndex(t => t.id === tabId);
      if (tabIndex !== -1) {
        const tab = tabs[tabIndex];

        // 【彻底修复数据互通问题】移除AI智能客服的特殊保护，所有标签页都可以关闭
        console.log(`移除标签页: ${tabId}，店铺: ${tab.shopId || currentShopId}`);
      
        // 移除标签元素，但保留webview
        tab.element.remove();
      
        // 隐藏视图元素而不是移除它
        tab.viewElement.style.display = 'none';
      
        // 从数组中移除
        tabs.splice(tabIndex, 1);

        // 【彻底修复数据互通问题】移除全局跟踪逻辑

        // 更新当前店铺的标签页状态
        if (currentShopId) {
          shopTabs[currentShopId] = {
            tabs: [...tabs],
            activeTabId: activeTabId !== tabId ? activeTabId : null
          };
        }
      
        // 如果移除的是当前激活的标签页，且不是静默模式
        if (activeTabId === tabId && !silent) {
          if (tabs.length > 0) {
            // 激活下一个标签页或前一个标签页
            const nextTab = tabs[tabIndex] || tabs[tabIndex - 1];
            activateTab(nextTab.id);
          } else {
            // 没有标签页了，不显示任何内容
            activeTabId = null;
            // 【修复】移除自动创建AI智能客服标签页的逻辑
            // 当用户关闭所有标签页时，应该保持空白状态，让用户主动选择需要的功能
            console.log('所有标签页已关闭，等待用户操作');
          }
        }
      }
    }
  
    // 加载URL
    async function loadUrl(tabId, url, forceReload = false) {
      // 【关键修复】如果正在进行店铺切换，完全阻止URL加载
      if (isShopSwitching && !forceReload) {
        console.log(`店铺切换期间阻止URL加载: ${url} 到标签页 ${tabId}`);
        return;
      }

      console.log(`加载URL: ${url} 到标签页 ${tabId}，强制重新加载: ${forceReload}`);

      try {
        const tab = tabs.find(t => t.id === tabId);
        if (!tab) {
          console.error(`找不到标签页 ${tabId}`);
          return;
        }
      
        // 处理上架产品页面的URL修正
        if (url === 'https://shop.weixin.qq.com/merchant/product' || 
            url.includes('merchant/product') || 
            url.includes('上架产品')) {
          console.log('检测到上架产品页面请求，重定向到filehelper');
          url = 'https://filehelper.weixin.qq.com/';
        }
      
              // 检查是否是AI智能客服页面，更新标志
      if (url.includes('store.weixin.qq.com/shop/kf') || tab.title === 'AI智能客服' || tab.title === '店铺客服') {
        tab.isCustomerService = true;
        
          // 移除关闭按钮
          const closeBtn = tab.element.querySelector('.tab-close');
          if (closeBtn) {
            closeBtn.remove();
          }
        }
      
        tab.url = url;
      
        // 检查是否已经存在webview
        let webview = tab.viewElement.querySelector('webview');
      
        if (!webview) {
          console.log(`为标签页 ${tabId} 创建新的webview`);

          // 【关键修复】先确保主进程中的session分区已创建
          const shopId = tab.shopId || currentShopId || 'default';
          if (shopId !== 'default') {
            try {
              console.log(`🔧 确保店铺 ${shopId} 的session分区在主进程中已创建...`);
              const result = await window.xiaomeihuaAPI.ensureShopSession(shopId);
              if (!result.success) {
                console.error(`❌ 创建店铺session分区失败: ${result.message}`);
              } else {
                console.log(`✅ 店铺session分区已就绪: ${result.partition}`);
              }
            } catch (error) {
              console.error('确保店铺session分区时出错:', error);
            }
          }

          // 创建新的webview
          webview = document.createElement('webview');
          webview.className = 'embedded-webview';

          // 设置webview属性
          webview.setAttribute('src', url);
          // 【最终修复】移除 allowpopups 属性。
          // 此属性会直接允许弹窗，绕过 new-window 事件监听器。
          // 移除后，所有弹窗请求都将被阻塞，并强制触发 new-window 事件，从而被我们的监听器捕获。

          // 【彻底修复数据互通问题】为每个店铺使用独立的会话分区，确保数据完全隔离
          const shopPartition = `persist:shop_${shopId}`;
          console.log(`为标签页 ${tabId} 设置独立会话分区: ${shopPartition}，店铺ID: ${shopId}`);
          webview.setAttribute('partition', shopPartition);
          webview.setAttribute('webpreferences', 'nodeIntegration=no, contextIsolation=yes, allowRunningInsecureContent=yes, sandbox=no');
        
          // 为视频号助手页面设置标准Chrome的User-Agent
          if (url.includes('channels.weixin.qq.com/platform/live/home') || tab.title === '视频号助手') {
            console.log('检测到视频号助手页面，设置标准Chrome User-Agent');
            webview.setAttribute('useragent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36');
          }

          // 为AI知识库页面设置特殊的User-Agent
          if (url.includes('ai-knowledge.html') || tab.title === 'AI知识库') {
            console.log('检测到AI知识库页面，设置小梅花APP User-Agent');
            webview.setAttribute('useragent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 xiaomeihua-app/1.0 Electron');
          }
        
          // 添加到视图容器
          tab.viewElement.appendChild(webview);
        
          // 监听加载事件
          webview.addEventListener('did-start-loading', () => {
            console.log(`标签页 ${tabId} 开始加载: ${url}`);

            // 【新增】如果是微信小店页面，预先设置状态
            if (url.includes('store.weixin.qq.com') || url.includes('weixin.qq.com')) {
              console.log('🔄 微信小店页面开始加载，准备状态管理...');

              // 清除可能的错误计数
              if (window.weixinErrorCount) {
                const errorKeys = Object.keys(window.weixinErrorCount).filter(key => key.startsWith(tabId));
                errorKeys.forEach(key => delete window.weixinErrorCount[key]);
              }
            }
          });
        
          // 【全新修复方案】使用 dom-ready 事件和 executeJavaScript 注入脚本
          // 这可以绕过页面的内容安全策略 (CSP)
          webview.addEventListener('dom-ready', async () => {
            console.log(`[WebView] 标签页 ${tabId} DOM准备就绪，准备注入链接拦截脚本...`);
            try {
              const scriptContent = await window.xiaomeihuaAPI.getPreloadScriptContent();
              if (scriptContent) {
                await webview.executeJavaScript(scriptContent);
                console.log(`[WebView] 成功将链接拦截脚本注入到 ${url}`);
              } else {
                console.error(`[WebView] 无法注入脚本：未能获取脚本内容。`);
              }
            } catch (error) {
              console.error(`[WebView] 注入脚本到 ${url} 时发生严重错误:`, error);
            }
          });
        
          // 【最终修复】恢复并强化 new-window 事件监听器
          // 这是拦截所有从 <webview> 内部发起的弹窗请求的唯一正确位置。
          webview.addEventListener('new-window', (e) => {
            // 总是阻止默认的弹窗行为
            e.preventDefault();
          
            // 将请求转发到主进程，由主进程决定如何处理
            window.xiaomeihuaAPI.openUrl(e.url, e.disposition || '新页面');
          });
        
          // 注入链接点击拦截脚本
          webview.addEventListener('dom-ready', () => {
            console.log(`标签页 ${tabId} DOM已准备就绪`);

            // 【新增】检测微信小店页面的重定向问题
            if (url.includes('store.weixin.qq.com') || url.includes('weixin.qq.com')) {
              webview.executeJavaScript(`
                (function() {
                  // 检测页面是否出现重定向错误
                  function checkRedirectError() {
                    const errorElements = document.querySelectorAll('*');
                    for (let element of errorElements) {
                      const text = element.textContent || element.innerText || '';
                      if (text.includes('ERR_TOO_MANY_REDIRECTS') ||
                          text.includes('重定向次数过多') ||
                          text.includes('无法加载页面') ||
                          text.includes('加载失败')) {
                        console.log('🚨 检测到页面重定向错误');
                        return true;
                      }
                    }

                    // 检查URL是否异常
                    if (window.location.href.includes('error') ||
                        window.location.href.includes('redirect') ||
                        document.title.includes('错误') ||
                        document.title.includes('失败')) {
                      console.log('🚨 检测到URL或标题异常');
                      return true;
                    }

                    return false;
                  }

                  // 立即检查
                  if (checkRedirectError()) {
                    console.log('🔄 页面存在重定向错误，使用专用错误处理');

                    // 使用专用错误处理机制
                    if (typeof handleWeixinStoreError === 'function') {
                      handleWeixinStoreError('${tabId}', window.location.href, 'redirect_error', {
                        errorType: 'redirect_detected',
                        timestamp: Date.now()
                      });
                    }
                  }

                  // 设置定时检查
                  setTimeout(() => {
                    if (checkRedirectError()) {
                      console.log('🔄 延迟检查发现重定向错误，使用专用错误处理');

                      if (typeof handleWeixinStoreError === 'function') {
                        handleWeixinStoreError('${tabId}', window.location.href, 'redirect_error', {
                          errorType: 'redirect_detected_delayed',
                          timestamp: Date.now()
                        });
                      }
                    }
                  }, 2000);
                })();
              `).catch(err => console.error('注入重定向错误检测脚本失败:', err));
            }

            // 此处不再需要注入脚本，因为新的注入逻辑已经处理
          });
        
          // 监听页面大小变化，动态调整webview以适应窗口大小
          const resizeObserver = new ResizeObserver((entries) => {
            for (let entry of entries) {
              // 获取当前webview的容器大小
              const containerWidth = entry.contentRect.width;
              const containerHeight = entry.contentRect.height;
            
              // 设置统一的缩放比例为70%
              const scale = 0.7;
            
              // 应用缩放，同时确保完全填充
              webview.style.width = `${100 / scale}%`;
              webview.style.height = `${100 / scale}%`;
              webview.style.transform = `scale(${scale})`;
              webview.style.transformOrigin = 'top left';
              webview.style.position = 'absolute';
              webview.style.top = '0';
              webview.style.left = '0';
              webview.style.right = '0';
              webview.style.bottom = '0';
            }
          });
        
          // 开始观察大小变化
          resizeObserver.observe(tab.viewElement);
        
          // 拦截所有导航请求
          webview.addEventListener('will-navigate', (e) => {
            console.log('拦截导航请求:', e.url);
          
            // 【关键修复】完全避免调用webview.getURL()，这可能触发页面状态检查
            // 直接使用tab.url进行比较，这是最安全的方式
            if (e.url === tab.url) {
              console.log('允许页面刷新');
              return;
            }
          
            // 不再阻止默认导航，允许在当前标签页导航
            // e.preventDefault();

            // 【彻底修复页面刷新问题】完全禁止在will-navigate中更新tab.url
            // 这是导致店铺切换时页面刷新的根本原因之一
            console.log(`标签页 ${tabId} 导航事件: ${tab.url} -> ${e.url}，保持tab.url不变以避免店铺切换时的页面刷新`);

            // 【关键修复】完全不更新tab.url，让它保持原始状态
            // 这样在店铺切换时，保存和恢复的URL将保持一致，避免不必要的页面刷新
          
            // 特殊处理AI智能上架页面
            if (e.url.includes('merchant/product') || e.url.includes('上架产品') || e.url.includes('AI智能上架')) {
              console.log('检测到AI智能上架页面导航，重定向到filehelper');
              e.preventDefault(); // 这种情况下阻止默认导航

              // 【关键修复】避免在店铺切换期间设置webview.src
              if (!isShopSwitching) {
                webview.src = 'https://filehelper.weixin.qq.com/';
                tab.title = 'AI智能上架';
                tab.element.querySelector('.tab-title').textContent = 'AI智能上架';
              } else {
                console.log('店铺切换期间，延迟AI智能上架页面重定向');
              }
            }
          
            // 特殊处理视频号助手页面
            if (e.url.includes('channels.weixin.qq.com')) {
              console.log('检测到视频号助手页面导航，设置标准Chrome User-Agent');
              webview.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36');
            }

            // 特殊处理AI知识库页面
            if (e.url.includes('ai-knowledge.html')) {
              console.log('检测到AI知识库页面导航，设置小梅花APP User-Agent');
              webview.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 xiaomeihua-app/1.0 Electron');
            }
          });
        
          webview.addEventListener('did-navigate', (e) => {
            console.log(`标签页 ${tabId} 导航完成: ${e.url}`);
          
            // 特殊处理视频号助手页面
            if (e.url.includes('channels.weixin.qq.com')) {
              console.log('检测到视频号助手页面导航完成，确保设置标准Chrome User-Agent');
              webview.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36');
            }

            // 特殊处理AI知识库页面
            if (e.url.includes('ai-knowledge.html')) {
              console.log('检测到AI知识库页面导航完成，确保设置小梅花APP User-Agent');
              webview.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 xiaomeihua-app/1.0 Electron');
            }
          });
        
          webview.addEventListener('did-finish-load', () => {
            console.log(`标签页 ${tabId} 加载完成: ${url}`);

            // 检查是否是AI智能客服或AI智能上架页面，如果是则立即注入卡密连接器
            if (url.includes('store.weixin.qq.com') ||
                url.includes('shop.weixin.qq.com') ||
                url.includes('weixin.qq.com/shop') ||
                url.includes('filehelper.weixin.qq.com') ||
                url.includes('channels.weixin.qq.com')) { // 添加视频号助手页面
              console.log(`[小梅花] 检测到目标页面: ${url}，立即注入连接器...`);

              // 确保injectKamiConnector函数存在
              if (typeof injectKamiConnector === 'function') {
                // 立即注入，不再设置延迟注入
                injectKamiConnector(webview);
              } else {
                console.error('injectKamiConnector函数不存在');
              }

            // 如果是AI智能客服页面，添加登录状态监控
            if (url.includes('store.weixin.qq.com/shop/kf') || tab.title === 'AI智能客服' || tab.title === '店铺客服') {
                console.log('检测到AI智能客服页面，设置登录状态监控...');
              
                // 注入登录状态监控脚本
                webview.executeJavaScript(`
                  (function() {
                    // 避免重复注入
                    if (window.xmhLoginMonitorActive) {
                      console.log('登录状态监控已激活，跳过');
                      return;
                    }
                  
                    window.xmhLoginMonitorActive = true;
                    console.log('启动登录状态监控');
                  
                    // 定义登录状态检测函数
                    function checkLoginStatus() {
                      // 检查是否存在客服工作台元素，这表示已登录成功
                      const workbenchElements = document.querySelectorAll('.customer-workbench, .workbench, .service-workbench');
                      const headerElements = document.querySelectorAll('.header-shop-name, .shop-name, .store-name');
                    
                      // 检查是否有登录状态指示器
                      const hasLoginIndicator = document.querySelector('.login-info') || 
                                              document.querySelector('.user-info') || 
                                              document.querySelector('.avatar');
                                            
                      return (workbenchElements.length > 0 || headerElements.length > 0 || hasLoginIndicator !== null);
                    }
                  
                    // 记录上一次的登录状态
                    let wasLoggedIn = checkLoginStatus();
                  
                    // 监控DOM变化
                    const observer = new MutationObserver(function(mutations) {
                      // 检查当前登录状态
                      const isLoggedIn = checkLoginStatus();
                    
                      // 如果从未登录状态变为已登录状态，触发登录成功事件
                      if (!wasLoggedIn && isLoggedIn) {
                        console.log('检测到登录状态变化：用户已登录成功');
                      
                        // 触发自定义事件通知登录成功
                        const event = new CustomEvent('xmh-login-success', { 
                          detail: { 
                            title: document.title,
                            timestamp: Date.now()
                          } 
                        });
                        document.dispatchEvent(event);
                      
                        // 更新登录状态
                        wasLoggedIn = true;
                      }
                    
                      // 如果页面标题中不再包含"登录"或"扫码"，可能表示登录成功
                      if (!wasLoggedIn && document.title && !document.title.includes('登录') && !document.title.includes('扫码')) {
                        // 再次检查登录状态指示器
                        setTimeout(() => {
                          if (checkLoginStatus()) {
                            console.log('检测到页面标题更新且找到登录指示器，确认已登录成功');
                          
                            // 触发自定义事件通知登录成功
                            const event = new CustomEvent('xmh-login-success', { 
                              detail: { 
                                title: document.title,
                                timestamp: Date.now()
                              } 
                            });
                            document.dispatchEvent(event);
                          
                            // 更新登录状态
                            wasLoggedIn = true;
                          }
                        }, 1000);
                      }
                    });
                  
                    // 观察整个文档的变化
                    observer.observe(document.documentElement, {
                      childList: true,
                      subtree: true,
                      attributes: true,
                      characterData: false
                    });
                  
                    // 如果页面已经加载完成并且已经登录，立即触发事件
                    if (checkLoginStatus()) {
                      console.log('页面已加载完成且已登录，立即触发登录成功事件');
                    
                      setTimeout(() => {
                        const event = new CustomEvent('xmh-login-success', { 
                          detail: { 
                            title: document.title,
                            timestamp: Date.now()
                          } 
                        });
                        document.dispatchEvent(event);
                      
                        // 更新登录状态
                        wasLoggedIn = true;
                      }, 500);
                    }
                  
                    // 定期检查登录状态（以防DOM变化监控未能捕捉到登录状态变化）
                    setInterval(() => {
                      const isLoggedIn = checkLoginStatus();
                      if (!wasLoggedIn && isLoggedIn) {
                        console.log('定期检查发现用户已登录成功');
                      
                        // 触发自定义事件通知登录成功
                        const event = new CustomEvent('xmh-login-success', { 
                          detail: { 
                            title: document.title,
                            timestamp: Date.now()
                          } 
                        });
                        document.dispatchEvent(event);
                      
                        // 更新登录状态
                        wasLoggedIn = true;
                      }
                    }, 2000); // 每2秒检查一次
                  })();
                `).catch(err => {
                  console.error('注入登录监控脚本失败:', err);
                });
              
                // 监听登录成功事件
                webview.addEventListener('ipc-message', (event) => {
                  if (event.channel === 'xmh-login-success') {
                    console.log('收到登录成功事件:', event.args[0]);

                    // 【关键修复】检查是否正在切换店铺，如果是则跳过检测
                    if (isShopSwitching) {
                      console.log('🔄 正在切换店铺，跳过登录成功事件的店铺检测');
                      return;
                    }

                    // 【优化】每次扫码登录都强制执行店铺检测，不跳过已检测的店铺
                    console.log('🔄 扫码登录成功，强制执行店铺检测（清除之前的检测状态）');

                    // 标记最近登录时间
                    markRecentLogin();

                    // 清除当前店铺的检测状态，确保重新检测
                    if (currentShopId) {
                      clearShopCheckStatusForShop(currentShopId);
                    }

                    // 登录成功后立即强制检测店铺名称匹配
                    setTimeout(() => {
                      if (!isShopSwitching) { // 再次检查切换状态
                        checkShopNameMatch(webview, true); // 强制检测
                      }
                    }, 1000);

                    // 多次检测确保成功
                    setTimeout(() => {
                      if (!isShopSwitching) { // 再次检查切换状态
                        checkShopNameMatch(webview, true); // 强制检测
                      }
                    }, 3000);

                    setTimeout(() => {
                      if (!isShopSwitching) { // 再次检查切换状态
                        checkShopNameMatch(webview, true); // 强制检测
                      }
                    }, 5000);
                  }
                });
              
                // 添加IPC消息处理
                webview.executeJavaScript(`
                  document.addEventListener('xmh-login-success', function(e) {
                    console.log('检测到登录成功，发送IPC消息');
                    if (window.ipcRenderer && window.ipcRenderer.send) {
                      window.ipcRenderer.send('xmh-login-success', {
                        title: e.detail.title,
                        timestamp: e.detail.timestamp
                      });
                    } else {
                      console.error('ipcRenderer不可用，尝试通过标题发送消息');
                      // 使用标题作为备用通信方式
                      const originalTitle = document.title;
                      document.title = 'XMH_IPC_MSG::' + JSON.stringify({
                        channel: 'xmh-login-success',
                        data: {
                          title: e.detail.title,
                          timestamp: e.detail.timestamp
                        }
                      });
                      // 恢复原始标题
                      setTimeout(() => {
                        document.title = originalTitle;
                      }, 100);
                    }
                  });
                `).catch(err => {
                  console.error('添加IPC消息处理失败:', err);
                });
              }
            }
          
            // 特殊处理视频号助手页面
            if (url.includes('channels.weixin.qq.com')) {
              console.log('检测到视频号助手页面加载完成，注入布局优化脚本');
            
              // 注入脚本修复右侧空白问题并确保二维码正常显示
              webview.executeJavaScript(`
                (function() {
                  console.log('视频号助手页面优化脚本开始执行');
                
                  // 修复右侧空白问题和滚动问题
                  function fixLayoutIssues() {
                    // 全局样式修复
                    const style = document.createElement('style');
                    style.textContent = 
                      "html, body {" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  width: 100% !important;" +
                      "  height: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  overflow-y: auto !important;" +
                      "}" +
                    
                      "body > div {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "}" +
                    
                      ".main-container, .container, .content-container," + 
                      "[class*='container']:not(#control-panel):not(#auth-panel):not(#success-popup), [class*='wrapper']," + 
                      "[class*='content']:not(.panel-body):not(.popup-body), [class*='layout'] {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  margin: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "  overflow-x: hidden !important;" +
                      "  overflow-y: auto !important;" +
                      "}" +
                    
                      ".flex-container:not(#control-panel):not(#auth-panel):not(#success-popup), [style*='display: flex']:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon) {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "}" +
                    
                      // 专门针对登录页面的样式
                      ".login-container, .qrcode-container, .qrcode-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  height: auto !important;" +
                      "  overflow: visible !important;" +
                      "}" +
                    
                      // 针对二维码的特殊处理
                      ".qrcode-container, [class*='qrcode'], [class*='qr-code'], [class*='QRCode'], " +
                      "div:has(img[src*='qrcode']), div:has(img[src*='qr_code']), " +
                      "div:has(canvas[id*='qr']), div:has(canvas[id*='QR']) {" +
                      "  position: fixed !important;" +
                      "  right: 20px !important;" +
                      "  top: 50% !important;" +
                      "  transform: translateY(-50%) !important;" +
                      "  z-index: 9999 !important;" +
                      "  visibility: visible !important;" +
                      "  opacity: 1 !important;" +
                      "}" +
                    
                      // 修复菜单栏收起/展开按钮点击后页面右侧出现空白的问题
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}" +
                    
                      "body > div > div:not(#control-panel):not(#auth-panel):not(#success-popup):not(#floating-icon), .app-container, .main-content, .content-wrapper {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  min-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 修复微信小店页面的布局问题
                      ".westore-app, .westore-app > div, .westore-layout, .westore-content {" +
                      "  width: 100% !important;" +
                      "  max-width: 100% !important;" +
                      "  overflow-x: hidden !important;" +
                      "}" +
                    
                      // 修复菜单收起/展开后的闪动问题
                      ".sidebar-toggle, .menu-toggle, [class*='toggle']:not(#minimize-panel-btn), [class*='collapse'] {" +
                      "  transition: none !important;" +
                      "}" +
                    
                      // 防止内容溢出
                      "* {" +
                      "  max-width: 100vw !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      // 保护脚本注入的UI元素不受页面样式影响
                      "#control-panel {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  z-index: 99998 !important;" +
                      "  background: rgba(255,255,255,0.95) !important;" +
                      "  border: 1px solid #ddd !important;" +
                      "  border-radius: 10px !important;" +
                      "  box-shadow: 0 5px 20px rgba(0,0,0,0.2) !important;" +
                      "  width: 320px !important;" +
                      "  height: auto !important;" +
                      "  font-family: sans-serif !important;" +
                      "  backdrop-filter: blur(10px) !important;" +
                      "  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 320px !important;" +
                      "  min-width: 320px !important;" +
                      "}" +
                    
                      "#control-panel .panel-header {" +
                      "  background: linear-gradient(135deg, #28a745, #20c997) !important;" +
                      "  color: white !important;" +
                      "  padding: 15px !important;" +
                      "  border-top-left-radius: 10px !important;" +
                      "  border-top-right-radius: 10px !important;" +
                      "  font-size: 16px !important;" +
                      "  font-weight: bold !important;" +
                      "  position: relative !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .panel-body {" +
                      "  padding: 20px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "  margin: 0 !important;" +
                      "}" +
                    
                      "#control-panel .status-item {" +
                      "  margin-bottom: 10px !important;" +
                      "  padding: 8px !important;" +
                      "  background: #f8f9fa !important;" +
                      "  border-radius: 5px !important;" +
                      "  width: 100% !important;" +
                      "  box-sizing: border-box !important;" +
                      "}" +
                    
                      "#floating-icon {" +
                      "  position: fixed !important;" +
                      "  top: 20px !important;" +
                      "  left: 20px !important;" +
                      "  width: 60px !important;" +
                      "  height: 60px !important;" +
                      "  border-radius: 50% !important;" +
                      "  cursor: pointer !important;" +
                      "  z-index: 99999 !important;" +
                      "  background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382) !important;" +
                      "  background-size: 300% 300% !important;" +
                      "  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;" +
                      "  transform: none !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  max-width: 60px !important;" +
                      "  min-width: 60px !important;" +
                      "  max-height: 60px !important;" +
                      "  min-height: 60px !important;" +
                      "}" +
                    
                      "#auth-panel, #success-popup {" +
                      "  position: fixed !important;" +
                      "  top: 50% !important;" +
                      "  left: 50% !important;" +
                      "  transform: translate(-50%, -50%) !important;" +
                      "  z-index: 99999 !important;" +
                      "  width: 380px !important;" +
                      "  max-width: 380px !important;" +
                      "  min-width: 380px !important;" +
                      "  height: auto !important;" +
                      "  margin: 0 !important;" +
                      "  padding: 0 !important;" +
                      "  border-radius: 15px !important;" +
                      "}");
                  
                    document.head.appendChild(style);
                    console.log('全局样式修复已应用');
                  }
                
                  // 立即执行一次修复
                  fixLayoutIssues();
                
                  // 监听DOM变化，在DOM变化后重新应用修复
                  const observer = new MutationObserver((mutations) => {
                    fixLayoutIssues();
                  
                    // 检查并修复脚本注入的UI元素
                    fixScriptUIElements();
                  });
                
                  // 开始观察DOM变化
                  observer.observe(document.body, {
                    childList: true,
                    subtree: true
                  });
                
                  // 监听窗口大小变化，重新应用修复
                  window.addEventListener('resize', () => {
                    fixLayoutIssues();
                    fixScriptUIElements();
                  });
                
                  // 定期执行修复，确保UI元素正常显示
                  function fixScriptUIElements() {
                    // 修复浮动图标
                    const floatingIcon = document.getElementById('floating-icon');
                    if (floatingIcon) {
                      Object.assign(floatingIcon.style, {
                        position: 'fixed',
                        top: '20px',
                        left: '20px',
                        width: '60px',
                        height: '60px',
                        borderRadius: '50%',
                        zIndex: '99999',
                        transform: 'none',
                        margin: '0',
                        padding: '0',
                        maxWidth: '60px',
                        minWidth: '60px',
                        maxHeight: '60px',
                        minHeight: '60px'
                      });
                    }
                  
                    // 修复控制面板
                    const controlPanel = document.getElementById('control-panel');
                    if (controlPanel) {
                      Object.assign(controlPanel.style, {
                        position: 'fixed',
                        top: '20px',
                        left: '20px',
                        width: '320px',
                        maxWidth: '320px',
                        minWidth: '320px',
                        zIndex: '99998',
                        transform: 'none',
                        margin: '0',
                        padding: '0'
                      });
                    
                      // 修复面板头部
                      const panelHeader = controlPanel.querySelector('.panel-header');
                      if (panelHeader) {
                        Object.assign(panelHeader.style, {
                          width: '100%',
                          boxSizing: 'border-box',
                          margin: '0',
                          padding: '15px'
                        });
                      }
                    
                      // 修复面板主体
                      const panelBody = controlPanel.querySelector('.panel-body');
                      if (panelBody) {
                        Object.assign(panelBody.style, {
                          width: '100%',
                          boxSizing: 'border-box',
                          margin: '0',
                          padding: '20px'
                        });
                      }
                    }
                  }
                
                  // 延迟执行，确保在页面完全加载后应用修复
                  setTimeout(fixLayoutIssues, 1000);
                  setTimeout(fixLayoutIssues, 2000);
                  setTimeout(fixLayoutIssues, 5000);
                
                  // 定期检查并修复UI元素
                  setInterval(fixScriptUIElements, 2000);
                
                  console.log('视频号助手页面优化脚本执行完成');
                })();
              `);
            }
          });
        
          webview.addEventListener('did-fail-load', (e) => {
            console.error(`标签页 ${tabId} 加载失败:`, e);

            // 【增强】检测微信小店重定向错误
            if (e.errorCode === -310 || e.errorDescription.includes('ERR_TOO_MANY_REDIRECTS')) {
              console.log('🚨 检测到微信小店重定向错误，执行紧急修复...');

              // 【关键修复】立即执行完全重置
              handleCriticalRedirectError(tabId, url, {
                errorCode: e.errorCode,
                errorDescription: e.errorDescription
              });
              return;
            }

            // 【新增】检测微信小店网络连接错误
            if ((url.includes('store.weixin.qq.com') || url.includes('weixin.qq.com')) &&
                (e.errorCode === -106 || e.errorCode === -105 || e.errorCode === -21)) {
              console.log('🚨 检测到微信小店网络连接错误，尝试重新连接...');

              if (handleWeixinStoreError(tabId, url, 'network_error', {
                errorCode: e.errorCode,
                errorDescription: e.errorDescription
              })) {
                return;
              }
            }

            // 【新增】检测其他微信小店相关错误
            if (url.includes('store.weixin.qq.com') || url.includes('weixin.qq.com')) {
              // 检测登录相关错误
              if (e.errorDescription.includes('登录') || e.errorDescription.includes('认证') ||
                  e.errorDescription.includes('授权') || e.errorCode === -200) {
                console.log('🚨 检测到微信小店登录错误');

                if (handleWeixinStoreError(tabId, url, 'login_failed', {
                  errorCode: e.errorCode,
                  errorDescription: e.errorDescription
                })) {
                  return;
                }
              }

              // 检测会话过期错误
              if (e.errorDescription.includes('会话') || e.errorDescription.includes('过期') ||
                  e.errorCode === -401 || e.errorCode === -403) {
                console.log('🚨 检测到微信小店会话过期');

                if (handleWeixinStoreError(tabId, url, 'session_expired', {
                  errorCode: e.errorCode,
                  errorDescription: e.errorDescription
                })) {
                  return;
                }
              }

              // 其他微信小店错误
              if (e.errorCode < 0 && e.errorCode !== -3 && e.errorCode !== -102) {
                console.log('🚨 检测到微信小店其他错误');

                if (handleWeixinStoreError(tabId, url, 'generic_error', {
                  errorCode: e.errorCode,
                  errorDescription: e.errorDescription
                })) {
                  return;
                }
              }
            }

            // 如果是AI智能上架页面，尝试重定向到正确的URL
            if (url.includes('merchant/product') || url.includes('上架产品') || url.includes('AI智能上架') ||
                tab.title === 'AI智能上架' || tab.title === '上架产品' ||
                tab.element.querySelector('.tab-title').textContent === 'AI智能上架' ||
                tab.element.querySelector('.tab-title').textContent === '上架产品') {
              console.log('检测到AI智能上架页面加载失败，尝试重定向到filehelper...');

              // 【关键修复】避免在店铺切换期间设置webview.src
              if (!isShopSwitching) {
                webview.src = 'https://filehelper.weixin.qq.com/';
              } else {
                console.log('店铺切换期间，延迟AI智能上架页面重定向');
              }
              return;
            }

            // 忽略错误码 -3（取消导航）和 -102（连接中断，通常是用户导航到其他页面）
            // 这些通常是正常的导航行为，不需要显示错误页面
            if (e.errorCode === -3 || e.errorCode === -102) {
              console.log(`忽略非关键错误: ${e.errorCode} (${e.errorDescription})`);
              return;
            }

            // 显示错误页面
            try {
              webview.executeJavaScript(`
                document.body.innerHTML = '<div style="padding: 20px; text-align: center;"><h2>加载失败</h2><p>无法加载页面: ${url}</p><p>错误代码: ${e.errorCode}</p><p>错误描述: ${e.errorDescription}</p><button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px; background: #1a73e8; color: white; border: none; border-radius: 4px; cursor: pointer;">重试</button></div>';
                `).catch(err => console.error('注入错误页面失败:', err));
            } catch(err) {
              console.error('创建错误页面失败:', err);
            }
          });
        
          // =================================================================
          // 【修复】移除此处多余的、引起冲突的 new-window 事件监听器。
          // 所有新窗口请求将由 main.js中的 setWindowOpenHandler 统一处理。
          // =================================================================
        
          // 【最终修复方案】通过监听标题变化来接收来自<webview>的命令
          // 这是一个比postMessage更可靠的通信渠道
          let originalTitle = ''; // 用于存储原始标题
          webview.addEventListener('page-title-updated', (e) => {
            const newTitle = e.title;
            const commandPrefix = 'XMH_NAV_CMD::';

            if (newTitle.startsWith(commandPrefix)) {
              console.log('[Renderer] 检测到标题命令，准备处理...');
              // 提取并解析命令
              const commandJSON = newTitle.substring(commandPrefix.length);
              try {
                const command = JSON.parse(commandJSON);
                if (command && command.url) {
                  // 执行命令：在主进程中打开新标签页
                  window.xiaomeihuaAPI.openUrl(command.url, command.title || '新页面');
                }
              } catch (err) {
                console.error('[Renderer] 解析标题命令失败:', err);
              }
              // 立即将标题恢复原状，避免用户看到命令
              if (originalTitle) {
                webview.executeJavaScript(`document.title = "${originalTitle.replace(/"/g, '\\"')}"`);
              }
            } else {
              // 如果不是命令，就更新原始标题
              originalTitle = newTitle;
            }
          });
        
          // 监听页面标题变化
          webview.addEventListener('page-title-updated', (e) => {
            console.log(`标签页 ${tabId} 标题更新: ${e.title}`);
          
            // 检查是否是IPC消息
            if (e.title && e.title.startsWith('XMH_IPC_MSG::')) {
              try {
                const ipcData = JSON.parse(e.title.replace('XMH_IPC_MSG::', ''));
                console.log('收到IPC消息:', ipcData.channel, ipcData.data);
              
                // 处理登录成功消息
                if (ipcData.channel === 'xmh-login-success') {
                  console.log('收到登录成功IPC消息:', ipcData.data);

                  // 【关键修复】检查是否正在切换店铺，如果是则跳过检测
                  if (isShopSwitching) {
                    console.log('🔄 正在切换店铺，跳过登录成功IPC消息的店铺检测');
                    return;
                  }

                  // 【优化】每次扫码登录都强制执行店铺检测，不跳过已检测的店铺
                  console.log('🔄 扫码登录成功（IPC消息），强制执行店铺检测（清除之前的检测状态）');

                  // 标记最近登录时间
                  markRecentLogin();

                  // 清除当前店铺的检测状态，确保重新检测
                  if (currentShopId) {
                    clearShopCheckStatusForShop(currentShopId);
                  }

                  // 登录成功后立即强制检测店铺名称匹配
                  setTimeout(() => {
                    if (!isShopSwitching) { // 再次检查切换状态
                      checkShopNameMatch(webview, true); // 强制检测
                    }
                  }, 1000);

                  // 多次检测确保成功
                  setTimeout(() => {
                    if (!isShopSwitching) { // 再次检查切换状态
                      checkShopNameMatch(webview, true); // 强制检测
                    }
                  }, 3000);

                  setTimeout(() => {
                    if (!isShopSwitching) { // 再次检查切换状态
                      checkShopNameMatch(webview, true); // 强制检测
                    }
                  }, 5000);
                }
              
                // 不更新标签页标题
                return;
              } catch (err) {
                console.error('解析IPC消息失败:', err);
              }
            }
          
            // 检查是否是导航命令
            if (e.title && e.title.startsWith('XMH_NAV_CMD::')) {
              // 这是导航命令，不更新标签页标题
              return;
            }
          
            // 更新标签页标题
            tab.title = e.title;
            if (tab.element.querySelector('.tab-title')) {
              tab.element.querySelector('.tab-title').textContent = e.title;
            }
          
                      // 如果是AI智能客服页面，检测店铺名称是否匹配
          if (url.includes('store.weixin.qq.com/shop/kf') || tab.title === 'AI智能客服' || tab.title === '店铺客服') {
            console.log('AI智能客服页面标题更新，检测店铺名称匹配...');

              // 【关键修复】如果正在切换店铺，跳过所有检测
              if (isShopSwitching) {
                console.log('🔄 正在切换店铺，跳过AI智能客服页面的店铺检测');
                return;
              }

              // 如果标题包含"登录"或"扫码"字样，说明还未登录，暂不检测
              if (e.title.includes('登录') || e.title.includes('扫码')) {
                console.log('检测到登录页面，暂不检测店铺名称匹配');
                return;
              }

              // 【优化】检查是否是刚刚扫码登录成功的情况
              const isRecentLogin = checkRecentLoginActivity();

              // 【关键修复】只有在真正的扫码登录时才执行检测
              if (isRecentLogin && !isShopSwitching) {
                console.log('🔄 检测到扫码登录活动，执行店铺检测');

                // 延迟1秒后检测，确保页面完全加载
                setTimeout(() => {
                  // 再次检查是否在切换店铺，确保不会误检测
                  if (!isShopSwitching) {
                    checkShopNameMatch(webview, true); // 强制检测
                  } else {
                    console.log('检测时发现正在切换店铺，取消检测');
                  }
                }, 1000);
              } else {
                console.log(`店铺 ${currentShopId} 已检测或非扫码登录，跳过AI智能客服页面的检测`);
              }
            }
          
                      // 如果标题变为AI智能客服或AI智能上架，注入卡密连接器
          if (e.title.includes('AI智能客服') || e.title.includes('店铺客服') || 
              e.title.includes('AI智能上架') || e.title.includes('上架产品') ||
              e.title.includes('视频号助手')) {
              console.log(`[小梅花] 检测到标题更新为目标页面: ${e.title}，注入连接器...`);
            
              // 确保injectKamiConnector函数存在
              if (typeof injectKamiConnector === 'function') {
                injectKamiConnector(webview);
              } else {
                console.error('injectKamiConnector函数不存在');
              }
            }
          });
        
          // 监听控制台消息
          webview.addEventListener('console-message', (e) => {
            // 【修复】显示所有级别的日志，而不仅仅是错误
            console.log(`[WebView Console][L${e.level}] ${e.message} (源: ${e.sourceId}:${e.line})`);
          });
        } else {
          // 【彻底修复店铺切换页面刷新问题】如果webview已存在，检查是否需要更新
          const currentSrcUrl = webview.getAttribute('src');

          // 【关键修复】只有在强制重新加载或URL确实不同时才更新
          if (forceReload || currentSrcUrl !== url) {
            // 进一步检查是否是同一个页面的不同状态
            const isSamePage = currentSrcUrl && url &&
              (url.split('?')[0].split('#')[0] === currentSrcUrl.split('?')[0].split('#')[0]);

            if (forceReload || !isSamePage) {
              console.log(`为标签页 ${tabId} 更新webview URL: ${currentSrcUrl} -> ${url}，强制重新加载: ${forceReload}`);

              // 【关键修复】避免在店铺切换期间设置webview.src
              if (!isShopSwitching) {
                // 只有在强制重新加载或真正需要导航到不同页面时才更新
                webview.src = url;
              } else {
                console.log('店铺切换期间，延迟webview URL更新');
              }
            } else {
              console.log(`标签页 ${tabId} 的webview在同一页面，无需刷新。当前: ${currentSrcUrl}, 目标: ${url}`);
            }
          } else {
            console.log(`标签页 ${tabId} 的webview URL完全相同，无需更新: ${url}`);
          }
        }
      
        console.log(`标签页 ${tabId} URL加载完成`);
      } catch (error) {
        console.error(`加载URL到标签页 ${tabId} 失败:`, error);
      }
    }
  
    // =================================================================
    // 【关键修复】添加IPC监听器，处理从主进程发来的创建标签页请求
    // =================================================================
    window.xiaomeihuaAPI.onCreateTab(({ url, title }) => { // 【修复】修正了回调函数的参数
      console.log(`[STEP 1/5 - COMPLETE] Renderer(main.html) 收到 create-tab 事件, URL: ${url}, 正在创建标签...`);
      if (url) {
        createTab(title || '新页面', url);
      } else {
        console.error('[IPC RENDERER] 错误: 收到的URL无效。');
      }
    });
  
    // =================================================================
    // 【URL匹配算法】支持通配符匹配的URL验证函数
    // =================================================================
    function matchUrl(url, pattern) {
      if (!pattern || !url) return false;

      // 如果模式是 * 则匹配所有URL
      if (pattern === '*') return true;

      // 简单的通配符匹配
      if (pattern.includes('*')) {
        // 将通配符替换为正则表达式
        const regexPattern = pattern.replace(/\*/g, '.*');
        const regex = new RegExp('^' + regexPattern + '$', 'i');
        return regex.test(url);
      }

      // 精确匹配
      return url.toLowerCase() === pattern.toLowerCase();
    }

    // 检查当前URL是否匹配任何URL规则
    function shouldLoadScriptForUrl(url, urlPatterns) {
      if (!url) {
        console.log('[URL匹配] URL为空，不加载脚本');
        return false;
      }

      if (!urlPatterns || !Array.isArray(urlPatterns) || urlPatterns.length === 0) {
        console.log('[URL匹配] 没有URL规则，默认不加载脚本');
        return false;
      }

      console.log(`[URL匹配] 检查URL: ${url}`);
      console.log(`[URL匹配] URL规则:`, urlPatterns);

      for (let i = 0; i < urlPatterns.length; i++) {
        const pattern = urlPatterns[i];
        if (matchUrl(url, pattern)) {
          console.log(`[URL匹配] ✅ 匹配成功: ${url} 匹配规则 "${pattern}"`);
          return true;
        } else {
          console.log(`[URL匹配] ❌ 不匹配: ${url} 不匹配规则 "${pattern}"`);
        }
      }

      console.log(`[URL匹配] ❌ 所有规则都不匹配，不加载脚本`);
      return false;
    }

    // 在webview内部使用的URL匹配函数（简化版，避免作用域问题）
    function shouldLoadScriptForCurrentUrl(url, urlPatterns) {
      if (!url) {
        console.log('[小梅花加载器] URL为空，不加载脚本');
        return false;
      }

      if (!urlPatterns || !Array.isArray(urlPatterns) || urlPatterns.length === 0) {
        console.log('[小梅花加载器] 没有URL规则，默认不加载脚本');
        return false;
      }

      console.log('[小梅花加载器] 检查URL:', url);
      console.log('[小梅花加载器] URL规则:', urlPatterns);

      for (let i = 0; i < urlPatterns.length; i++) {
        const pattern = urlPatterns[i];

        // 如果模式是 * 则匹配所有URL
        if (pattern === '*') {
          console.log('[小梅花加载器] ✅ 通配符匹配成功');
          return true;
        }

        // 简单的通配符匹配
        try {
          if (pattern.includes('*')) {
            // 将通配符替换为正则表达式
            const regexPattern = pattern.replace(/\*/g, '.*');
            const regex = new RegExp('^' + regexPattern + '$', 'i');
            if (regex.test(url)) {
              console.log('[小梅花加载器] ✅ 匹配成功:', url, '匹配规则', pattern);
              return true;
            }
          } else {
            // 精确匹配
            if (url.toLowerCase() === pattern.toLowerCase()) {
              console.log('[小梅花加载器] ✅ 匹配成功:', url, '匹配规则', pattern);
              return true;
            }
          }
          console.log('[小梅花加载器] ❌ 不匹配:', url, '不匹配规则', pattern);
        } catch (e) {
          console.error('[小梅花加载器] URL匹配错误:', e.message, '模式:', pattern);
        }
      }

      console.log('[小梅花加载器] ❌ 所有规则都不匹配，不加载脚本');
      return false;
    }

    // =================================================================
    // 【已修复】注入卡密连接器函数 (V4 - 添加URL匹配验证)
    // =================================================================
    function injectKamiConnector(webview) {
      try {
        console.log('[小梅花] 准备注入卡密连接器 (V4 - 支持URL匹配)...');

        // 获取当前页面URL用于调试
        const currentUrl = webview.getURL();
        console.log(`[小梅花] 当前页面URL: ${currentUrl}`);

        // 检查是否有店铺不匹配记录
        checkShopMismatchRecord();

        // 1. 获取许可证密钥
        const licenseKey = shopInfo ? shopInfo.license : null;
        if (!licenseKey) {
          console.error('[小梅花] 注入失败: 未找到许可证密钥 (shopInfo.license)');
        
          // 尝试从API获取许可证密钥
          window.xiaomeihuaAPI.getLicenseKey().then(key => {
            if (key) {
              console.log(`[小梅花] 通过API获取到许可证密钥: ${key.substring(0, 8)}...`);
              injectWithKey(webview, key);
            } else {
              console.error('[小梅花] 无法通过API获取许可证密钥');
            }
          }).catch(err => {
            console.error('[小梅花] 获取许可证密钥失败:', err);
          });
          return;
        }
      
        injectWithKey(webview, licenseKey);
      } catch (error) {
        console.error('[小梅花] injectKamiConnector 函数本身发生错误:', error);
      }
    
      // 使用许可证密钥注入脚本
      function injectWithKey(webview, licenseKey) {
        try {
          console.log(`[小梅花] 发现密钥: ${licenseKey.substring(0, 8)}...`);

          // 获取绑定的店铺名称
          const boundShopName = shopInfo ? shopInfo.shopName : null;
          if (!boundShopName) {
            console.error('[小梅花] 警告: 未找到绑定的店铺名称');
          } else {
            console.log(`[小梅花] 卡密绑定的店铺名称: ${boundShopName}`);
          }

          // 检查webview是否已经注入过
          webview.executeJavaScript('window.xiaomeihuaLoaderHasRun === true')
            .then(hasRun => {
              if (hasRun) {
                console.log('[小梅花] 连接器已注入，无需重复注入');
                return;
              }
            
              // 检查是否有缓存的脚本
              const cachedScriptKey = `xiaomeihua_cached_script_${licenseKey}`;
              let cachedScript = localStorage.getItem(cachedScriptKey);
              let cachedTimestamp = localStorage.getItem(`${cachedScriptKey}_timestamp`);
              const now = Date.now();
            
              // 如果有缓存的脚本且未过期（10分钟内有效）
              if (cachedScript && cachedTimestamp && (now - parseInt(cachedTimestamp)) < 600000) {
                console.log('[小梅花] 发现缓存的脚本，检查URL匹配规则...');

                // 获取缓存的URL规则
                const cachedUrlPatternsKey = `xiaomeihua_cached_url_patterns_${licenseKey}`;
                let cachedUrlPatterns = [];
                try {
                  const cachedUrlPatternsStr = localStorage.getItem(cachedUrlPatternsKey);
                  if (cachedUrlPatternsStr) {
                    cachedUrlPatterns = JSON.parse(cachedUrlPatternsStr);
                  }
                } catch (e) {
                  console.error('[小梅花] 解析缓存的URL规则失败:', e);
                  console.error('[小梅花] 缓存的URL规则原始数据:', localStorage.getItem(cachedUrlPatternsKey));
                }

                // 检查当前页面是否匹配URL规则
                const currentUrl = webview.getURL();
                console.log(`[小梅花] 缓存脚本URL匹配检查: ${currentUrl}`);
                console.log(`[小梅花] 缓存的URL规则:`, cachedUrlPatterns);

                // 特殊处理：如果没有缓存的URL规则，记录警告但允许加载（向后兼容）
                if (!cachedUrlPatterns || !Array.isArray(cachedUrlPatterns) || cachedUrlPatterns.length === 0) {
                  console.warn('[小梅花] ⚠️ 没有缓存的URL匹配规则，为了向后兼容，将继续加载缓存脚本');
                  injectCachedScript(webview, licenseKey, cachedScript);
                } else if (shouldLoadScriptForUrl(currentUrl, cachedUrlPatterns)) {
                  console.log('[小梅花] ✅ 缓存脚本URL匹配验证通过，使用缓存脚本');
                  injectCachedScript(webview, licenseKey, cachedScript);
                } else {
                  console.log('[小梅花] ❌ 缓存脚本URL匹配验证失败，当前页面不应加载脚本');
                }
              } else {
                // 没有缓存或缓存过期，执行正常注入流程
                injectFreshScript(webview, licenseKey);
              }
            })
            .catch(err => {
              console.error('[小梅花] 检查注入状态失败，执行注入:', err);
              injectFreshScript(webview, licenseKey);
            });
        } catch (error) {
          console.error('[小梅花] injectWithKey 函数发生错误:', error);
        }
      }
    
      // 使用缓存的脚本注入
      function injectCachedScript(webview, licenseKey, cachedScript) {
        const loaderScript = `
          (function() {
            // --- 防止重复注入 ---
            if (window.xiaomeihuaLoaderHasRun) {
              console.log('[小梅花加载器] 已注入，跳过。');
              return;
            }
            window.xiaomeihuaLoaderHasRun = true;
            console.log('[小梅花加载器] 开始执行(缓存版)... 密钥: ${licenseKey.substring(0, 8)}...');

            // --- 模拟油猴环境 API (关键步骤) ---
            window.GM_setValue = (key, value) => {
              try { localStorage.setItem('GM_.' + key, JSON.stringify(value)); } catch (e) { console.error('GM_setValue 失败:', e); }
            };
            window.GM_getValue = (key, defaultValue) => {
              try {
                const value = localStorage.getItem('GM_.' + key);
                return value === null ? defaultValue : JSON.parse(value);
              } catch (e) { return defaultValue; }
            };
            window.GM_deleteValue = (key) => {
              try { localStorage.removeItem('GM_.' + key); } catch (e) { console.error('GM_deleteValue 失败:', e); }
            };
            window.GM_addStyle = (css) => {
              const style = document.createElement('style');
              style.textContent = css;
              document.head.appendChild(style);
              return style;
            };
            window.GM_openInTab = (url, options) => {
              return window.open(url, '_blank');
            };
            window.GM_xmlhttpRequest = (details) => {
              const { method = 'GET', url, headers = {}, data, onload, onerror } = details;
              fetch(url, { method, headers, body: data })
                .then(response => response.text().then(responseText => ({ response, responseText })))
                .then(({ response, responseText }) => {
                  if (onload) {
                    onload({
                      status: response.status,
                      statusText: response.statusText,
                      responseText: responseText,
                      responseHeaders: response.headers,
                      finalUrl: response.url,
                    });
                  }
                })
                .catch(error => {
                  if (onerror) onerror(error);
                });
            };
            console.log('[小梅花加载器] 油猴 API 模拟层已准备就绪。');

            // 预先存储卡密，因为加载的脚本会从这里读取
            window.GM_setValue('saved_license_key_xiaomeihua', "${licenseKey}");

            // 执行缓存的脚本
            try {
              const scriptElement = document.createElement('script');
              scriptElement.textContent = ${JSON.stringify(cachedScript)};
              document.head.appendChild(scriptElement);
              console.log('[小梅花加载器] 缓存的功能脚本执行完毕。');
            } catch (e) {
              console.error('[小梅花加载器] 执行缓存脚本失败:', e);
            }
          })();
        `;
      
        // 注入并执行脚本
        webview.executeJavaScript(loaderScript)
          .then(() => {
            console.log('[小梅花] 缓存连接器脚本注入成功。');
          })
          .catch(err => {
            console.error('[小梅花] 注入缓存脚本时发生错误:', err);
          });
      }
    
      // 从服务器获取新脚本并注入
      function injectFreshScript(webview, licenseKey) {
        // 2. 构建注入脚本
        // 此脚本将在 <webview> 内部执行，模拟油猴脚本的验证和加载流程
        const loaderScript = `
          (function() {
            // --- 防止重复注入 ---
            if (window.xiaomeihuaLoaderHasRun) {
              console.log('[小梅花加载器] 已注入，跳过。');
              return;
            }
            window.xiaomeihuaLoaderHasRun = true;
            console.log('[小梅花加载器] 开始执行... 密钥: ${licenseKey.substring(0, 8)}...');

            // --- 模拟油猴环境 API (关键步骤) ---
            window.GM_setValue = (key, value) => {
              try { localStorage.setItem('GM_.' + key, JSON.stringify(value)); } catch (e) { console.error('GM_setValue 失败:', e); }
            };
            window.GM_getValue = (key, defaultValue) => {
              try {
                const value = localStorage.getItem('GM_.' + key);
                return value === null ? defaultValue : JSON.parse(value);
              } catch (e) { return defaultValue; }
            };
            window.GM_deleteValue = (key) => {
              try { localStorage.removeItem('GM_.' + key); } catch (e) { console.error('GM_deleteValue 失败:', e); }
            };
            window.GM_addStyle = (css) => {
              const style = document.createElement('style');
              style.textContent = css;
              document.head.appendChild(style);
              return style;
            };
            window.GM_openInTab = (url, options) => {
              return window.open(url, '_blank');
            };
            window.GM_xmlhttpRequest = (details) => {
              const { method = 'GET', url, headers = {}, data, onload, onerror } = details;
              fetch(url, { method, headers, body: data })
                .then(response => response.text().then(responseText => ({ response, responseText })))
                .then(({ response, responseText }) => {
                  if (onload) {
                    onload({
                      status: response.status,
                      statusText: response.statusText,
                      responseText: responseText,
                      responseHeaders: response.headers,
                      finalUrl: response.url,
                    });
                  }
                })
                .catch(error => {
                  if (onerror) onerror(error);
                });
            };
            console.log('[小梅花加载器] 油猴 API 模拟层已准备就绪。');

            // 预先存储卡密，因为加载的脚本会从这里读取
            window.GM_setValue('saved_license_key_xiaomeihua', "${licenseKey}");

            // --- 核心验证逻辑 ---
            const verifyUrl = 'https://xiaomeihuakefu.cn/api/verify.php';
            console.log(\`[小梅花加载器] 向 \${verifyUrl} 发送验证请求...\`);

            fetch(verifyUrl, {
              method: 'POST',
              headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
              body: 'key=' + encodeURIComponent("${licenseKey}")
            })
            .then(response => {
              if (!response.ok) {
                throw new Error('服务器响应错误: ' + response.status);
              }
              return response.json();
            })
            .then(data => {
              console.log('[小梅花加载器] 收到服务器响应:', data);
              if (data && data.success && data.script) {
                console.log('[小梅花加载器] 验证成功，检查URL匹配规则...');

                // 【V4 新增】检查URL匹配规则
                const currentUrl = window.location.href;
                const urlPatterns = data.url_patterns || [];

                console.log('[小梅花加载器] 当前页面URL:', currentUrl);
                console.log('[小梅花加载器] 服务器返回的URL规则:', urlPatterns);
                console.log('[小梅花加载器] URL规则类型:', typeof urlPatterns, '是否为数组:', Array.isArray(urlPatterns));

                // 特殊处理：如果没有URL规则，记录警告但不阻止脚本加载（向后兼容）
                if (!urlPatterns || !Array.isArray(urlPatterns) || urlPatterns.length === 0) {
                  console.warn('[小梅花加载器] ⚠️ 服务器未返回有效的URL匹配规则，为了向后兼容，将继续加载脚本');
                  console.warn('[小梅花加载器] 建议在后台为此脚本配置URL匹配规则以提高安全性');
                } else {
                  // 检查是否应该在当前页面加载脚本
                  if (!shouldLoadScriptForCurrentUrl(currentUrl, urlPatterns)) {
                    console.log('[小梅花加载器] ❌ 当前页面不匹配URL规则，跳过脚本加载');
                    return;
                  }
                }

                console.log('[小梅花加载器] ✅ URL匹配验证通过，开始执行脚本...');

                // 缓存脚本和URL规则到localStorage
                try {
                  localStorage.setItem('xiaomeihua_cached_script_${licenseKey}', data.script);
                  localStorage.setItem('xiaomeihua_cached_script_${licenseKey}_timestamp', Date.now().toString());
                  localStorage.setItem('xiaomeihua_cached_url_patterns_${licenseKey}', JSON.stringify(urlPatterns));
                  console.log('[小梅花加载器] 脚本和URL规则已缓存');
                } catch (e) {
                  console.error('[小梅花加载器] 缓存脚本失败:', e);
                }

                // =======================================================
                // 【V4 核心优化】添加URL匹配验证的脚本执行方式
                // =======================================================
                const originalFetch = window.fetch;
                const originalXHR = window.XMLHttpRequest;
                console.log('[小梅花加载器] 原生 fetch 和 XHR 已备份。');

                try {
                  // 直接执行脚本而不是使用new Function
                  const scriptElement = document.createElement('script');
                  scriptElement.textContent = data.script;
                  document.head.appendChild(scriptElement);
                  console.log('[小梅花加载器] 功能脚本执行完毕。');
                } catch (e) {
                  console.error('[小梅花加载器] 执行服务器脚本失败:', e);
                  alert('小梅花助手功能脚本执行失败，请联系客服。错误: ' + e.message);
                } finally {
                  // 无论成功与否，都恢复原生函数，确保宿主页面正常工作
                  setTimeout(() => {
                      window.fetch = originalFetch;
                      window.XMLHttpRequest = originalXHR;
                      console.log('[小梅花加载器] 原生 fetch 和 XHR 已恢复。页面功能将正常运行。');
                  }, 500); // 延迟确保功能脚本完成初始化
                }
              
              } else {
                console.error('[小梅花加载器] 卡密验证失败或脚本为空。消息: ' + (data.message || '未知错误'));
                console.error('[小梅花加载器] 服务器响应详情:', data);
                console.warn('[小梅花加载器] 验证失败，将尝试使用本地缓存或继续运行');
              }
            })
            .catch(error => {
              console.error('[小梅花加载器] 网络请求失败:', error);
              console.error('[小梅花加载器] 错误详情:', {
                name: error.name,
                message: error.message,
                stack: error.stack
              });
              console.warn('[小梅花加载器] 验证服务器连接失败，将继续尝试使用本地缓存');
            });
          })();
        `;
      
        // 3. 注入并执行脚本
        webview.executeJavaScript(loaderScript)
          .then(() => {
            console.log('[小梅花] 连接器脚本注入成功。');
          })
          .catch(err => {
            console.error('[小梅花] 注入脚本时发生严重错误:', err);
          });
      }
    }

    // 【修复】添加防抖机制，防止双击导致重复操作
    let menuClickTimeout = null;
    let lastClickedAction = null;
    let lastClickTime = 0;

    // 菜单点击事件
    menuItems.forEach(item => {
      item.addEventListener('click', function() {
        const action = this.getAttribute('data-action');
        const currentTime = Date.now();

        // 处理一级菜单的展开/收起
        if (action === 'wechat-store' || action === 'douyin-store') {
          const submenuId = action + '-submenu';
          const submenu = document.getElementById(submenuId);
          const parent = this;
          const sidebar = document.getElementById('sidebar');
          const isCollapsed = sidebar.classList.contains('collapsed');

          if (submenu) {
            if (isCollapsed) {
              // 收起状态下的处理逻辑 - 简单的切换显示/隐藏
              const isShowing = submenu.classList.contains('collapsed-show');

              // 先隐藏所有收起状态下的二级菜单
              document.querySelectorAll('.submenu').forEach(sub => {
                sub.classList.remove('collapsed-show');
              });

              if (!isShowing) {
                // 显示当前二级菜单
                submenu.classList.add('collapsed-show');
              }
            } else {
              // 展开状态下的原有逻辑
              const isExpanded = submenu.classList.contains('expanded');

              if (isExpanded) {
                // 收起
                submenu.classList.remove('expanded');
                parent.classList.remove('expanded');
              } else {
                // 展开前先收起其他菜单
                document.querySelectorAll('.submenu').forEach(sub => {
                  sub.classList.remove('expanded');
                });
                document.querySelectorAll('.menu-parent').forEach(par => {
                  par.classList.remove('expanded');
                });

                // 展开当前菜单
                submenu.classList.add('expanded');
                parent.classList.add('expanded');
              }
            }
          }
          return; // 一级菜单不执行后续的功能逻辑
        }

        // 【防抖机制】如果是相同操作且在500ms内，则忽略
        if (lastClickedAction === action && (currentTime - lastClickTime) < 500) {
          console.log(`防抖：忽略重复的${action}操作`);
          return;
        }

        // 清除之前的超时
        if (menuClickTimeout) {
          clearTimeout(menuClickTimeout);
        }

        // 更新最后点击信息
        lastClickedAction = action;
        lastClickTime = currentTime;

        // 移除所有二级菜单项的激活状态
        document.querySelectorAll('.submenu-item').forEach(mi => mi.classList.remove('active'));

        // 激活当前菜单项（如果是二级菜单项）
        if (this.classList.contains('submenu-item')) {
          this.classList.add('active');
        }

        // 【新增】保存当前店铺的菜单状态
        if (currentShopId) {
          saveCurrentShopMenuState();
        }
      
        // 执行相应操作
        switch (action) {
          case 'customer-service':
            // 【彻底修复数据互通问题】每个店铺独立的AI智能客服
            console.log(`[菜单点击] AI智能客服菜单被点击，当前店铺ID: ${currentShopId}`);

            // 构建包含店铺ID的AI智能客服URL
            const customerServiceUrl = `https://store.weixin.qq.com/shop/kf?shop_id=${currentShopId || ''}`;
            console.log(`[菜单点击] 目标URL: ${customerServiceUrl}`);

            // 检查当前店铺是否已有AI智能客服标签页
            const existingCustomerTab = tabs.find(t =>
              (t.url && t.url.includes('store.weixin.qq.com/shop/kf')) ||
              (t.title === 'AI智能客服' || t.title === '店铺客服')
            );

            if (existingCustomerTab) {
              console.log(`[菜单点击] 当前店铺已有AI智能客服标签页: ${existingCustomerTab.id}`);

              // 【优化】将AI智能客服标签页移动到第一位
              moveTabToFirst(existingCustomerTab.id);

              // 激活现有标签页
              activateTab(existingCustomerTab.id);
            } else {
              console.log(`[菜单点击] 当前店铺没有AI智能客服标签页，创建新的`);
              // 创建新的AI智能客服标签页
              const tabId = createTab('AI智能客服', customerServiceUrl, true, currentShopId);
              console.log(`[菜单点击] 为店铺 ${currentShopId} 创建了独立的AI智能客服标签页: ${tabId}`);
            }
            break;
          
          case 'product-upload':
            // 检查是否已存在当前店铺的AI智能上架标签页
            const existingProductTab = tabs.find(t => 
              ((t.url && t.url.includes('filehelper.weixin.qq.com')) || 
               t.title === 'AI智能上架' || t.title === '上架产品') && 
              t.shopId === currentShopId
            );
            
            if (existingProductTab) {
              console.log(`当前店铺(${currentShopId})的AI智能上架标签页已存在，直接激活`);
              activateTab(existingProductTab.id);
            } else {
              // 关闭其他店铺的AI智能上架标签页
              const otherProductTabs = tabs.filter(t => 
                (t.title === 'AI智能上架' || t.title === '上架产品') && 
                t.shopId !== currentShopId
              );
              
              otherProductTabs.forEach(tab => {
                removeTab(tab.id, true); // 静默移除，不激活其他标签
              });
              
              console.log(`为当前店铺(${currentShopId})创建新的AI智能上架标签页`);
              // 构建包含店铺ID的AI智能上架URL
              const productUrl = `https://filehelper.weixin.qq.com/?shop_id=${currentShopId || ''}`;
              createTab('AI智能上架', productUrl, true, currentShopId);
            }
            break;
          
          case 'ai-knowledge':
            // 检查是否已存在AI知识库标签页
            // 构建包含店铺ID的知识库URL - 指向本地文件
            const knowledgeBaseUrl = `https://xiaomeihuakefu.cn/ai-knowledge.html?shop_id=${currentShopId || ''}`;

            // 检查是否已存在当前店铺的AI知识库标签页
            const existingAITab = tabs.find(t =>
              (t.url && t.url.includes('ai-knowledge.html') &&
               t.url.includes(`shop_id=${currentShopId}`)) ||
              (t.title === 'AI知识库' && t.shopId === currentShopId)
            );

            if (existingAITab) {
              console.log(`当前店铺(${currentShopId})的AI知识库标签页已存在，直接激活`);
              activateTab(existingAITab.id);
            } else {
              // 关闭其他店铺的知识库标签页
              const otherAITabs = tabs.filter(t =>
                t.title === 'AI知识库' && t.shopId !== currentShopId
              );

              otherAITabs.forEach(tab => {
                removeTab(tab.id, true); // 静默移除，不激活其他标签
              });

              console.log(`为当前店铺(${currentShopId})创建新的AI知识库标签页`);
              createTab('AI知识库', knowledgeBaseUrl, true, currentShopId);
            }
            break;
          
          case 'video-channels':
            // 检查是否已存在当前店铺的视频号助手标签页
            const existingVideoTab = tabs.find(t => 
              (t.url.includes('channels.weixin.qq.com') && t.shopId === currentShopId) || 
              (t.title === '视频号助手' && t.shopId === currentShopId)
            );
            
            if (existingVideoTab) {
              console.log(`当前店铺(${currentShopId})的视频号助手标签页已存在，直接激活`);
              activateTab(existingVideoTab.id);
            } else {
              // 关闭其他店铺的视频号助手标签页
              const otherVideoTabs = tabs.filter(t => 
                t.title === '视频号助手' && t.shopId !== currentShopId
              );
              
              otherVideoTabs.forEach(tab => {
                removeTab(tab.id, true); // 静默移除，不激活其他标签
              });
              
              console.log(`为当前店铺(${currentShopId})创建新的视频号助手标签页`);
              // 构建包含店铺ID的视频号助手URL
              const videoChannelUrl = `https://channels.weixin.qq.com/login.html?shop_id=${currentShopId || ''}`;
              createTab('视频号助手', videoChannelUrl, true, currentShopId);
            }
            break;
          
          case 'settings':
            showSettings();
            break;
        }
      });
    });
  
    // 【新增】隐藏浏览器导航栏
    function hideBrowserTabs() {
      const tabsContainer = document.getElementById('tabs');
      if (tabsContainer) {
        tabsContainer.style.display = 'none';
        console.log('浏览器导航栏已隐藏');
      }
    }

    // 【新增】显示浏览器导航栏
    function showBrowserTabs() {
      const tabsContainer = document.getElementById('tabs');
      if (tabsContainer) {
        tabsContainer.style.display = 'flex';
        console.log('浏览器导航栏已显示');
      }
    }

    // 【新增】检查当前是否在特殊页面（设置或AI知识库）
    function isInSpecialPage() {
      // 检查设置面板是否显示
      const settingsPanel = document.getElementById('settings-panel');
      if (settingsPanel && settingsPanel.style.display === 'block') {
        return true;
      }

      // 检查当前激活的标签页是否是AI知识库
      if (activeTabId) {
        const activeTab = tabs.find(t => t.id === activeTabId);
        if (activeTab && (activeTab.title === 'AI知识库' ||
            (activeTab.url && activeTab.url.includes('ai-knowledge.html')))) {
          return true;
        }
      }

      return false;
    }

    // 【新增】处理在特殊页面中点击店铺名称的逻辑
    function handleSpecialPageShopClick(shopId, shopName) {
      console.log(`在特殊页面中点击店铺: ${shopName} (ID: ${shopId})`);

      // 如果点击的是当前店铺，查找该店铺已打开的网页并切换到最后一个活跃的标签页
      if (shopId === currentShopId) {
        // 查找当前店铺的所有标签页，排除AI知识库
        const shopWebTabs = tabs.filter(t =>
          t.shopId === currentShopId &&
          t.title !== 'AI知识库' &&
          !t.url.includes('ai-knowledge.html')
        );

        if (shopWebTabs.length > 0) {
          // 优先选择AI智能客服标签页
          let targetTab = shopWebTabs.find(t =>
            t.title === 'AI智能客服' ||
            t.title === '店铺客服' ||
            t.url.includes('store.weixin.qq.com/shop/kf')
          );

          // 如果没有AI智能客服，选择最后一个标签页
          if (!targetTab) {
            targetTab = shopWebTabs[shopWebTabs.length - 1];
          }

          console.log(`切换到店铺网页: ${targetTab.title}`);
          activateTab(targetTab.id);
        } else {
          // 如果没有其他标签页，创建AI智能客服标签页
          console.log('没有找到其他网页标签页，创建AI智能客服标签页');
          const customerServiceUrl = `https://store.weixin.qq.com/shop/kf?shop_id=${currentShopId}`;
          createTab('AI智能客服', customerServiceUrl, true, currentShopId);
        }
      } else {
        // 如果点击的是其他店铺，先切换店铺，然后打开该店铺的网页
        console.log(`切换到其他店铺: ${shopName}`);
        switchShop(shopId, shopName);
      }
    }

    // 显示设置面板
    function showSettings() {
      // 隐藏所有浏览器视图
      tabs.forEach(tab => {
        tab.viewElement.classList.remove('active');
      });

      // 显示设置面板
      settingsPanel.style.display = 'block';

      // 【新增】隐藏浏览器导航栏
      hideBrowserTabs();
    }
  
    // 【新增】创建自定义退出登录确认对话框
    function createLogoutConfirmDialog() {
      return new Promise((resolve) => {
        // 创建对话框样式
        const styleId = 'logout-confirm-dialog-style';
        if (!document.getElementById(styleId)) {
          const style = document.createElement('style');
          style.id = styleId;
          style.textContent = `
            .logout-confirm-overlay {
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.5);
              z-index: 10000;
              display: flex;
              align-items: center;
              justify-content: center;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .logout-confirm-dialog {
              background: white;
              border-radius: 12px;
              padding: 32px 24px 24px 24px;
              min-width: 320px;
              max-width: 400px;
              box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
              text-align: center;
            }

            .logout-confirm-logo {
              width: 64px;
              height: 64px;
              margin: 0 auto 20px auto;
              display: block;
              border-radius: 12px;
            }

            .logout-confirm-title {
              font-size: 18px;
              font-weight: 600;
              color: #333;
              margin-bottom: 12px;
              display: none; /* 隐藏文字标题，使用logo代替 */
            }

            .logout-confirm-message {
              font-size: 16px;
              color: #333;
              margin-bottom: 32px;
              line-height: 1.5;
              font-weight: 500;
            }

            .logout-confirm-buttons {
              display: flex;
              gap: 12px;
              justify-content: center;
            }

            .logout-confirm-btn {
              padding: 10px 24px;
              border: none;
              border-radius: 6px;
              font-size: 14px;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.2s ease;
              min-width: 80px;
            }

            .logout-confirm-btn.cancel {
              background: #f5f5f5;
              color: #666;
            }

            .logout-confirm-btn.cancel:hover {
              background: #e8e8e8;
            }

            .logout-confirm-btn.confirm {
              background: #ff4757;
              color: white;
            }

            .logout-confirm-btn.confirm:hover {
              background: #ff3742;
            }
          `;
          document.head.appendChild(style);
        }

        // 创建对话框HTML
        const overlay = document.createElement('div');
        overlay.className = 'logout-confirm-overlay';
        overlay.innerHTML = `
          <div class="logout-confirm-dialog">
            <img src="../assets/logo.png" alt="小梅花AI" class="logout-confirm-logo" />
            <div class="logout-confirm-title">退出登录</div>
            <div class="logout-confirm-message">确定要退出登录吗？</div>
            <div class="logout-confirm-buttons">
              <button class="logout-confirm-btn cancel">取消</button>
              <button class="logout-confirm-btn confirm">确定</button>
            </div>
          </div>
        `;

        // 添加事件监听器
        const cancelBtn = overlay.querySelector('.cancel');
        const confirmBtn = overlay.querySelector('.confirm');

        cancelBtn.onclick = () => {
          overlay.remove();
          resolve(false);
        };

        confirmBtn.onclick = () => {
          overlay.remove();
          resolve(true);
        };

        // 点击背景关闭对话框
        overlay.onclick = (e) => {
          if (e.target === overlay) {
            overlay.remove();
            resolve(false);
          }
        };

        // 添加到页面
        document.body.appendChild(overlay);

        // 聚焦到确定按钮
        setTimeout(() => {
          confirmBtn.focus();
        }, 100);
      });
    }



    // 退出登录
    logoutBtn.addEventListener('click', async () => {
      // 使用自定义对话框替代原生confirm
      const confirmed = await createLogoutConfirmDialog();

      if (confirmed) {
        console.log('🔄 开始退出登录流程...');

        // 清除所有检测状态
        clearShopCheckStatus();
        console.log('✅ 已清除所有店铺检测状态');

        try {
          // 执行退出登录
          await window.xiaomeihuaAPI.logout();
          console.log('✅ 退出登录请求已发送');

          // 【关键修复】验证退出登录的完整性
          setTimeout(async () => {
            await verifyLogoutCompleteness();
          }, 2000);

        } catch (error) {
          console.error('❌ 退出登录失败:', error);
        }
      } else {
        console.log('用户取消了退出登录操作');
      }
    });

    // 【新增】验证退出登录的完整性
    async function verifyLogoutCompleteness() {
      console.log('🔍 验证退出登录的完整性...');

      try {
        // 检查是否还有活跃的微信小店标签页
        const weixinTabs = tabs.filter(tab =>
          tab.url && (tab.url.includes('store.weixin.qq.com') || tab.url.includes('weixin.qq.com'))
        );

        if (weixinTabs.length > 0) {
          console.log(`🔄 发现 ${weixinTabs.length} 个微信小店标签页，执行强制清理...`);

          for (const tab of weixinTabs) {
            const webview = tab.viewElement.querySelector('webview');
            if (webview) {
              // 强制重新加载到登录页面
              const cleanUrl = 'https://store.weixin.qq.com/';
              console.log(`🔄 强制重新加载标签页 ${tab.title} 到登录页面`);
              webview.src = cleanUrl;
              tab.url = cleanUrl;
            }
          }
        }

        // 通知主进程验证状态清理
        if (window.xiaomeihuaAPI.verifyLogoutState) {
          const verifyResult = await window.xiaomeihuaAPI.verifyLogoutState();
          if (verifyResult.success) {
            console.log('✅ 退出登录状态验证通过');
          } else {
            console.log('⚠️ 退出登录状态验证失败，执行补充清理');
          }
        }

        console.log('✅ 退出登录完整性验证完成');
      } catch (error) {
        console.error('❌ 退出登录完整性验证失败:', error);
      }
    }
  
    // 复制卡密按钮功能
    const copyLicenseBtn = document.getElementById('copy-license-btn');
    if (copyLicenseBtn) {
      copyLicenseBtn.addEventListener('click', () => {
        const licenseElement = document.getElementById('settings-license');
        if (licenseElement) {
          // 获取卡密文本，从.license-text元素中获取
          const licenseTextElement = licenseElement.querySelector('.license-text');
          const licenseText = licenseTextElement ? licenseTextElement.textContent : licenseElement.textContent;

          // 创建临时输入框用于复制
          const tempInput = document.createElement('input');
          tempInput.value = licenseText;
          document.body.appendChild(tempInput);
          tempInput.select();
          document.execCommand('copy');
          document.body.removeChild(tempInput);

          // 显示复制成功提示
          const originalText = copyLicenseBtn.textContent;
          copyLicenseBtn.textContent = '已复制';
          copyLicenseBtn.style.backgroundColor = '#28a745';

          // 2秒后恢复按钮文字
          setTimeout(() => {
            copyLicenseBtn.textContent = originalText;
            copyLicenseBtn.style.backgroundColor = '';
          }, 2000);
        }
      });
    }
  
    // 监听店铺信息事件
    const removeShopInfoListener = window.xiaomeihuaAPI.onShopInfo((info) => {
      shopInfo = info;
      init();
    });
  
    // 检测状态管理函数
    function getShopCheckStatus(shopId) {
      try {
        const statusData = localStorage.getItem('xmh_shop_check_success');
        if (statusData) {
          const status = JSON.parse(statusData);
          return status[shopId] || null;
        }
      } catch (err) {
        console.error('读取检测状态失败:', err);
      }
      return null;
    }

    function setShopCheckSuccess(shopId) {
      try {
        let statusData = {};
        const existingData = localStorage.getItem('xmh_shop_check_success');
        if (existingData) {
          statusData = JSON.parse(existingData);
        }
        statusData[shopId] = {
          timestamp: new Date().getTime(),
          checked: true
        };
        localStorage.setItem('xmh_shop_check_success', JSON.stringify(statusData));
        console.log(`店铺 ${shopId} 检测状态已标记为成功`);
      } catch (err) {
        console.error('设置检测状态失败:', err);
      }
    }

    function clearShopCheckStatus() {
      try {
        console.log('🧹 开始清除所有店铺检测状态...');

        // 清除店铺检测状态
        localStorage.removeItem('xmh_shop_check_success');
        localStorage.removeItem('xmh_shop_mismatch');

        // 【新增】清除顶部菜单栏店铺选择状态
        localStorage.removeItem('selected_shop_id');
        localStorage.removeItem('selected_shop_name');
        localStorage.removeItem('current_shop_info');
        localStorage.removeItem('shop_selection_state');

        // 【增强】清除所有店铺相关的localStorage项
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (
            key.includes('shop') ||
            key.includes('store') ||
            key.includes('xmh_') ||
            key.includes('xiaomeihua') ||
            key.includes('login') ||
            key.includes('session') ||
            key.includes('cookie') ||
            key.includes('auth') ||
            key.includes('token')
          )) {
            keysToRemove.push(key);
          }
        }

        // 批量移除店铺相关项
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
          console.log('已清除localStorage项: ' + key);
        });

        // 【增强】清除sessionStorage中的所有相关项
        const sessionKeysToRemove = [];
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key && (
            key.includes('shop') ||
            key.includes('store') ||
            key.includes('xmh_') ||
            key.includes('xiaomeihua') ||
            key.includes('login') ||
            key.includes('session') ||
            key.includes('cookie') ||
            key.includes('auth') ||
            key.includes('token')
          )) {
            sessionKeysToRemove.push(key);
          }
        }

        // 批量移除sessionStorage项
        sessionKeysToRemove.forEach(key => {
          sessionStorage.removeItem(key);
          console.log('已清除sessionStorage项: ' + key);
        });

        // 【新增】清除IndexedDB中的相关数据
        try {
          if (window.indexedDB) {
            const deleteDB = indexedDB.deleteDatabase('xiaomeihua_data');
            deleteDB.onsuccess = () => console.log('已清除IndexedDB数据');
            deleteDB.onerror = (e) => console.log('清除IndexedDB失败:', e);
          }
        } catch (e) {
          console.log('IndexedDB清除操作失败:', e);
        }

        // 【新增】重置全局变量
        currentShopId = null;
        currentSelectedShopName = null;
        isShopSwitching = false;

        // 【新增】重置店铺检测相关的全局状态
        if (typeof shopCheckStatusMap !== 'undefined') {
          shopCheckStatusMap.clear();
        }

        // 【新增】清除顶部菜单栏的active状态
        const menuItems = document.querySelectorAll('.shop-menu-item');
        menuItems.forEach(item => {
          item.classList.remove('active');
        });

        console.log('✅ 已清除所有店铺检测状态和选择记录');
      } catch (err) {
        console.error('清除检测状态失败:', err);
      }
    }

    // 【新增】清除特定店铺的检测状态
    function clearShopCheckStatusForShop(shopId) {
      try {
        console.log(`🧹 清除店铺 ${shopId} 的检测状态...`);

        const statusData = localStorage.getItem('xmh_shop_check_success');
        if (statusData) {
          const status = JSON.parse(statusData);
          if (status[shopId]) {
            delete status[shopId];
            localStorage.setItem('xmh_shop_check_success', JSON.stringify(status));
            console.log(`✅ 已清除店铺 ${shopId} 的检测状态`);
          }
        }

        // 清除店铺不匹配记录
        localStorage.removeItem('xmh_shop_mismatch');

      } catch (err) {
        console.error(`清除店铺 ${shopId} 检测状态失败:`, err);
      }
    }

    // 【新增】检查最近的登录活动
    function checkRecentLoginActivity() {
      try {
        // 【关键修复】如果正在切换店铺，直接返回false
        if (isShopSwitching) {
          console.log('🔄 正在切换店铺，不认为是最近登录活动');
          return false;
        }

        const recentLoginTime = localStorage.getItem('xmh_recent_login_time');
        if (recentLoginTime) {
          const loginTime = parseInt(recentLoginTime);
          const currentTime = Date.now();
          const timeDiff = currentTime - loginTime;

          // 【优化】缩短时间窗口到15秒，更精确地识别扫码登录
          if (timeDiff < 15000) {
            console.log(`🕒 检测到最近扫码登录活动，登录时间: ${new Date(loginTime).toLocaleTimeString()}，距离现在: ${Math.round(timeDiff/1000)}秒`);
            return true;
          } else {
            console.log(`🕒 登录时间超过15秒，不认为是最近登录活动，距离现在: ${Math.round(timeDiff/1000)}秒`);
          }
        }
        return false;
      } catch (err) {
        console.error('检查最近登录活动失败:', err);
        return false;
      }
    }

    // 【新增】标记最近登录时间
    function markRecentLogin() {
      try {
        // 【关键修复】只有在非店铺切换状态下才标记登录时间
        if (!isShopSwitching) {
          localStorage.setItem('xmh_recent_login_time', Date.now().toString());
          console.log('✅ 已标记最近登录时间');
        } else {
          console.log('🔄 正在切换店铺，跳过标记登录时间');
        }
      } catch (err) {
        console.error('标记最近登录时间失败:', err);
      }
    }

    // 【新增】清除店铺切换保护状态
    function clearShopSwitchingProtection() {
      try {
        isShopSwitching = false;
        console.log('✅ 已清除店铺切换保护状态');
      } catch (err) {
        console.error('清除店铺切换保护状态失败:', err);
      }
    }

    // 【新增】设置店铺切换保护状态
    function setShopSwitchingProtection() {
      try {
        isShopSwitching = true;
        // 清除最近登录标记，防止误触发检测
        localStorage.removeItem('xmh_recent_login_time');
        console.log('🔄 已设置店铺切换保护状态，清除登录标记');
      } catch (err) {
        console.error('设置店铺切换保护状态失败:', err);
      }
    }

    // 【彻底重写】清理所有webview缓存和状态
    function clearAllWebviewCache() {
      try {
        console.log('🧹 开始彻底清理所有webview缓存和状态...');

        tabs.forEach((tab, index) => {
          if (tab.webview) {
            try {
              console.log(`清理标签页 ${index} 的webview...`);

              // 1. 清理webview的历史记录
              tab.webview.clearHistory();

              // 2. 停止所有加载
              tab.webview.stop();

              // 3. 清理webview的存储和缓存
              tab.webview.executeJavaScript(`
                try {
                  console.log('开始清理webview内部存储...');

                  // 清理localStorage
                  const localKeys = Object.keys(localStorage);
                  localKeys.forEach(key => localStorage.removeItem(key));
                  console.log('已清理localStorage，清理项数:', localKeys.length);

                  // 清理sessionStorage
                  const sessionKeys = Object.keys(sessionStorage);
                  sessionKeys.forEach(key => sessionStorage.removeItem(key));
                  console.log('已清理sessionStorage，清理项数:', sessionKeys.length);

                  // 清理所有cookies
                  if (document.cookie) {
                    const cookies = document.cookie.split(";");
                    cookies.forEach(function(c) {
                      const eqPos = c.indexOf("=");
                      const name = eqPos > -1 ? c.substr(0, eqPos).trim() : c.trim();
                      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname;
                      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=." + window.location.hostname;
                    });
                    console.log('已清理所有cookies，清理项数:', cookies.length);
                  }

                  // 清理IndexedDB
                  if (window.indexedDB) {
                    indexedDB.databases().then(databases => {
                      databases.forEach(db => {
                        if (db.name) {
                          indexedDB.deleteDatabase(db.name);
                          console.log('已删除IndexedDB:', db.name);
                        }
                      });
                    }).catch(e => console.log('清理IndexedDB失败:', e));
                  }

                  // 清理WebSQL（如果支持）
                  if (window.openDatabase) {
                    try {
                      const db = window.openDatabase('', '', '', '');
                      if (db) {
                        db.transaction(tx => {
                          tx.executeSql('DROP TABLE IF EXISTS data');
                        });
                        console.log('已清理WebSQL');
                      }
                    } catch (e) {
                      console.log('清理WebSQL失败:', e);
                    }
                  }

                  console.log('webview内部存储清理完成');
                } catch (e) {
                  console.log('webview内部存储清理失败:', e);
                }
              `);

              // 4. 重新加载webview到空白页面
              setTimeout(() => {
                try {
                  tab.webview.src = 'about:blank';
                  console.log(`已重置标签页 ${index} 的webview到空白页面`);
                } catch (e) {
                  console.log(`重置标签页 ${index} webview失败:`, e);
                }
              }, 1000);

              console.log(`✅ 已清理标签页 ${index} 的webview`);
            } catch (e) {
              console.log(`清理标签页 ${index} webview失败:`, e);
            }
          }
        });

        console.log('✅ 所有webview缓存和状态清理完成');
      } catch (err) {
        console.error('❌ 清理webview缓存失败:', err);
      }
    }

    // 【新增】微信小店专用错误处理
    function handleWeixinStoreError(tabId, url, errorType, errorData = {}) {
      console.log(`🚨 处理微信小店错误: ${errorType}`, errorData);

      // 记录错误次数
      if (!window.weixinErrorCount) {
        window.weixinErrorCount = {};
      }

      const errorKey = `${tabId}_${errorType}`;
      window.weixinErrorCount[errorKey] = (window.weixinErrorCount[errorKey] || 0) + 1;

      // 如果错误次数超过3次，停止重试
      if (window.weixinErrorCount[errorKey] > 3) {
        console.log(`❌ 微信小店错误重试次数超限: ${errorType}`);
        return false;
      }

      // 根据错误类型执行不同的处理策略
      switch (errorType) {
        case 'redirect_error':
          return handleRedirectError(tabId, url, errorData);
        case 'login_failed':
          return handleLoginFailed(tabId, url, errorData);
        case 'session_expired':
          return handleSessionExpired(tabId, url, errorData);
        case 'network_error':
          return handleNetworkError(tabId, url, errorData);
        default:
          return handleGenericError(tabId, url, errorData);
      }
    }

    // 处理重定向错误
    function handleRedirectError(tabId, url, errorData) {
      console.log('🔄 处理微信小店重定向错误...');

      // 调用主进程清理状态
      window.xiaomeihuaAPI.handleWeixinRedirectError({
        url: url,
        errorType: 'redirect_error',
        tabId: tabId,
        timestamp: Date.now()
      }).then(result => {
        if (result.success) {
          console.log('✅ 重定向错误处理完成，延迟重新加载');

          // 延迟重新加载页面
          setTimeout(() => {
            const tab = tabs.find(t => t.id === tabId);
            if (tab && tab.viewElement) {
              const webview = tab.viewElement.querySelector('webview');
              if (webview && !isShopSwitching) {
                console.log('🔄 重新加载微信小店页面');
                webview.src = url;
              }
            }
          }, 5000);
        }
      }).catch(err => {
        console.error('❌ 重定向错误处理失败:', err);
      });

      return true;
    }

    // 处理登录失败
    function handleLoginFailed(tabId, url, errorData) {
      console.log('🔄 处理微信小店登录失败...');

      // 清理登录状态并重新初始化
      window.xiaomeihuaAPI.handleWeixinRedirectError({
        url: url,
        errorType: 'login_failed',
        tabId: tabId,
        timestamp: Date.now()
      }).then(result => {
        if (result.success) {
          console.log('✅ 登录失败处理完成');

          // 重新加载到登录页面
          setTimeout(() => {
            const tab = tabs.find(t => t.id === tabId);
            if (tab && tab.viewElement) {
              const webview = tab.viewElement.querySelector('webview');
              if (webview && !isShopSwitching) {
                console.log('🔄 重新加载到微信小店登录页面');
                webview.src = 'https://store.weixin.qq.com/';
              }
            }
          }, 3000);
        }
      }).catch(err => {
        console.error('❌ 登录失败处理失败:', err);
      });

      return true;
    }

    // 处理会话过期
    function handleSessionExpired(tabId, url, errorData) {
      console.log('🔄 处理微信小店会话过期...');

      // 清理会话状态
      window.xiaomeihuaAPI.handleWeixinRedirectError({
        url: url,
        errorType: 'session_expired',
        tabId: tabId,
        timestamp: Date.now()
      }).then(result => {
        if (result.success) {
          console.log('✅ 会话过期处理完成');
        }
      }).catch(err => {
        console.error('❌ 会话过期处理失败:', err);
      });

      return true;
    }

    // 处理网络错误
    function handleNetworkError(tabId, url, errorData) {
      console.log('🔄 处理微信小店网络错误...');

      // 先尝试清理可能的网络状态冲突
      window.xiaomeihuaAPI.handleWeixinRedirectError({
        url: url,
        errorType: 'network_error',
        tabId: tabId,
        timestamp: Date.now()
      }).then(result => {
        if (result.success) {
          console.log('✅ 网络错误处理完成');

          // 延迟重新加载页面
          setTimeout(() => {
            const tab = tabs.find(t => t.id === tabId);
            if (tab && tab.viewElement) {
              const webview = tab.viewElement.querySelector('webview');
              if (webview && !isShopSwitching) {
                console.log('🔄 重新加载微信小店页面');
                webview.src = url;
              }
            }
          }, 3000);
        }
      }).catch(err => {
        console.error('❌ 网络错误处理失败:', err);
      });

      return true;
    }

    // 【关键修复】处理严重的重定向错误
    function handleCriticalRedirectError(tabId, url, errorData) {
      console.log('🚨 处理严重的微信小店重定向错误...');

      // 1. 立即通知主进程执行完全清理
      window.xiaomeihuaAPI.handleWeixinRedirectError({
        url: url,
        errorType: 'critical_redirect_error',
        tabId: tabId,
        errorCode: errorData.errorCode,
        errorDescription: errorData.errorDescription,
        timestamp: Date.now()
      }).then(result => {
        if (result.success) {
          console.log('✅ 主进程清理完成，开始前端重置...');

          // 2. 立即执行前端完全重置
          setTimeout(() => {
            completelyResetWeixinStorePage(tabId);
          }, 1000);

          // 3. 延迟重新加载到登录页面
          setTimeout(() => {
            const tab = tabs.find(t => t.id === tabId);
            if (tab && tab.viewElement) {
              const webview = tab.viewElement.querySelector('webview');
              if (webview) {
                const cleanUrl = 'https://store.weixin.qq.com/';
                console.log('🔄 重新加载到干净的登录页面');
                webview.src = cleanUrl;
                tab.url = cleanUrl;
              }
            }
          }, 5000);

        } else {
          console.error('❌ 主进程清理失败，尝试前端强制重置');
          // 即使主进程清理失败，也尝试前端重置
          setTimeout(() => {
            completelyResetWeixinStorePage(tabId);
          }, 2000);
        }
      }).catch(err => {
        console.error('❌ 严重重定向错误处理失败:', err);
        // 发生错误时也尝试前端重置
        setTimeout(() => {
          completelyResetWeixinStorePage(tabId);
        }, 3000);
      });

      return true;
    }

    // 处理通用错误
    function handleGenericError(tabId, url, errorData) {
      console.log('🔄 处理微信小店通用错误...');

      // 延迟重试
      setTimeout(() => {
        const tab = tabs.find(t => t.id === tabId);
        if (tab && tab.viewElement) {
          const webview = tab.viewElement.querySelector('webview');
          if (webview && !isShopSwitching) {
            console.log('🔄 重试加载微信小店页面');
            webview.reload();
          }
        }
      }, 2000);

      return true;
    }

    // 【新增】完全重置所有webview
    function resetAllWebviews() {
      try {
        console.log('🔄 开始完全重置所有webview...');

        // 1. 先清理所有缓存
        clearAllWebviewCache();

        // 【新增】1.5. 清理微信小店专用会话状态
        try {
          console.log('🔄 清理微信小店会话状态...');
          window.xiaomeihuaAPI.handleWeixinRedirectError({
            url: 'reset_all_webviews',
            errorType: 'webview_reset',
            timestamp: Date.now()
          }).catch(err => {
            console.log('⚠️ 清理微信小店会话状态失败:', err);
          });
        } catch (err) {
          console.log('⚠️ 调用微信小店会话清理失败:', err);
        }

        // 2. 销毁所有现有的webview
        tabs.forEach((tab, index) => {
          if (tab.webview) {
            try {
              console.log(`销毁标签页 ${index} 的webview...`);

              // 移除所有事件监听器
              tab.webview.removeAllListeners();

              // 从DOM中移除
              if (tab.webview.parentNode) {
                tab.webview.parentNode.removeChild(tab.webview);
              }

              // 清空引用
              tab.webview = null;

              console.log(`✅ 已销毁标签页 ${index} 的webview`);
            } catch (e) {
              console.log(`销毁标签页 ${index} webview失败:`, e);
            }
          }
        });

        // 3. 清空所有标签页
        tabs.length = 0;

        // 4. 清理DOM中的webview容器
        const webviewContainer = document.querySelector('.webview-container');
        if (webviewContainer) {
          webviewContainer.innerHTML = '';
        }

        // 5. 清理标签页容器
        const tabContainer = document.querySelector('.tab-container');
        if (tabContainer) {
          tabContainer.innerHTML = '';
        }

        // 6. 重置全局状态
        currentShopId = null;
        currentSelectedShopName = null;
        isShopSwitching = false;

        console.log('✅ 所有webview完全重置完成');
      } catch (err) {
        console.error('❌ 完全重置webview失败:', err);
      }
    }

    // 【增强】检测店铺名称是否匹配
    function checkShopNameMatch(webview, forceDetection = false) {
      console.log(`🔍 检测店铺名称匹配... (强制检测: ${forceDetection})`);

      // 【关键修复】无论是否强制检测，都要检查店铺切换状态
      if (isShopSwitching) {
        console.log('🔄 正在切换店铺，跳过所有店铺检测（包括强制检测）');
        return;
      }

      // 【优化】如果是强制检测（扫码登录触发），跳过已检测状态的检查
      if (!forceDetection && currentShopId) {
        const checkStatus = getShopCheckStatus(currentShopId);
        if (checkStatus && checkStatus.checked) {
          console.log(`店铺 ${currentShopId} 已检测成功，跳过重复检测`);
          return;
        }
      }

      // 如果是强制检测，清除当前店铺的检测状态（而不是所有店铺）
      if (forceDetection) {
        console.log('🔄 强制检测模式（扫码登录触发），清除当前店铺的检测状态');
        if (currentShopId) {
          clearShopCheckStatusForShop(currentShopId);
        } else {
          // 如果没有当前店铺ID，清除店铺不匹配记录
          localStorage.removeItem('xmh_shop_mismatch');
        }
      }

      // 首先检查是否已经登录
      webview.executeJavaScript(`
        (function() {
          // 检查是否存在客服工作台元素，这表示已登录成功
          const workbenchElements = document.querySelectorAll('.customer-workbench, .workbench, .service-workbench');
          const headerElements = document.querySelectorAll('.header-shop-name, .shop-name, .store-name, .shop-info__header-name');
          
          // 检查是否有登录状态指示器
          const hasLoginIndicator = document.querySelector('.login-info') || 
                                  document.querySelector('.user-info') || 
                                  document.querySelector('.avatar');
                                  
          return {
            isLoggedIn: (workbenchElements.length > 0 || headerElements.length > 0 || hasLoginIndicator !== null),
            title: document.title
          };
        })()
      `)
      .then(result => {
        if (!result.isLoggedIn) {
          console.log('用户尚未登录，跳过店铺名称匹配检测');
          return;
        }
        
        console.log('用户已登录，开始检测店铺名称匹配...');
        
        // 获取当前选择的店铺名称和ID
        let currentSelectedShopName = '';
        let currentSelectedShopId = '';
        
        // 从当前选择的店铺获取信息
        if (currentShopId && shopInfo && shopInfo.isMultiStore && shopInfo.shops) {
          // 查找当前选择的店铺
          const selectedShop = shopInfo.shops.find(shop => shop.id === currentShopId);
          if (selectedShop) {
            currentSelectedShopName = selectedShop.name;
            currentSelectedShopId = selectedShop.id;
            console.log(`当前选择的店铺: ${currentSelectedShopName} (ID: ${currentSelectedShopId})`);
          }
        }
        
        // 如果没有找到当前选择的店铺，使用shopInfo中的shopName
        if (!currentSelectedShopName && shopInfo) {
          currentSelectedShopName = shopInfo.shopName;
          currentSelectedShopId = shopInfo.shopId;
          console.log(`使用默认店铺: ${currentSelectedShopName} (ID: ${currentSelectedShopId})`);
        }
        
        // 从页面标题中提取当前登录的店铺名称
        const currentTitle = result.title;
        let currentPageShopName = currentTitle;
        
        // 尝试从标题中提取更准确的店铺名称
        const shopNameMatch = currentTitle.match(/(.+?)(?:\s*-\s*.*)?$/);
        if (shopNameMatch && shopNameMatch[1]) {
          currentPageShopName = shopNameMatch[1].trim();
        }
        console.log(`当前页面标题: "${currentTitle}", 提取的店铺名称: "${currentPageShopName}"`);
        
        // 检查当前页面的店铺名称是否与当前选择的店铺名称匹配
        let isMatch = false;
        
        // 只检查是否匹配当前选择的店铺，不再检查其他卡密绑定的店铺
        if (currentSelectedShopName && currentPageShopName) {
          isMatch = currentPageShopName.includes(currentSelectedShopName) || 
                    currentSelectedShopName.includes(currentPageShopName);
          
          if (isMatch) {
            console.log(`当前页面店铺名称与选择的店铺 "${currentSelectedShopName}" 匹配`);
          } else {
            console.log(`当前页面店铺名称与选择的店铺 "${currentSelectedShopName}" 不匹配`);
            
            // 检查是否匹配卡密绑定的其他店铺（仅用于日志记录，不影响匹配结果）
            if (shopInfo && shopInfo.isMultiStore && shopInfo.shops) {
              for (const shop of shopInfo.shops) {
                if (shop.name && shop.id !== currentSelectedShopId && 
                    (currentPageShopName.includes(shop.name) || shop.name.includes(currentPageShopName))) {
                  console.log(`警告：当前页面店铺名称与卡密绑定的其他店铺 "${shop.name}" 匹配，但不是当前选择的店铺`);
                  break;
                }
              }
            }
          }
        }
        
        if (isMatch) {
          console.log('店铺名称匹配成功，无需提醒');
          // 标记当前店铺检测成功
          if (currentSelectedShopId) {
            setShopCheckSuccess(currentSelectedShopId);
          }
          return;
        }
        
        // 如果不匹配，显示警告弹窗并自动退出
        console.warn(`店铺名称不匹配! 页面标题: "${currentPageShopName}", 当前选择的店铺: "${currentSelectedShopName}"`);
        
        // 显示警告弹窗并执行自动退出
        webview.executeJavaScript(`
          (function() {
            // 注入公共函数到webview中
            ${createShopMismatchWarning.toString()}

            // 使用公共函数创建警告弹窗
            createShopMismatchWarning(
              "${currentSelectedShopName}",
              "${currentPageShopName}",
              true, // 显示自动退出提示
              function() {
                localStorage.removeItem('xmh_shop_mismatch');
                // 用户点击"我知道了"后才执行退出操作
                performLogout();
              }
            );

            // 保存店铺不匹配信息到localStorage，以便页面刷新后仍能显示
            localStorage.setItem('xmh_shop_mismatch', JSON.stringify({
              selectedShop: "${currentSelectedShopName}",
              currentPageShop: "${currentPageShopName}",
              timestamp: new Date().getTime()
            }));

            // 执行退出登录操作
            function performLogout() {
              console.log('执行自动退出登录操作...');

              // 方法1：尝试点击退出按钮
              try {
                // 先尝试点击设置按钮
                const settingsButtons = document.querySelectorAll('li.menu-list-tab');
                let settingsButton = null;
                
                // 查找设置按钮
                for (const button of settingsButtons) {
                  const svgPath = button.querySelector('svg path');
                  if (svgPath && svgPath.getAttribute('d') && svgPath.getAttribute('d').startsWith('M12.5631 3.2H11.4369')) {
                    settingsButton = button;
                    break;
                  }
                }
                
                if (settingsButton) {
                  // 点击设置按钮
                  settingsButton.click();
                  console.log('已点击设置按钮');
                  
                  // 等待菜单出现，然后点击退出登录
                  setTimeout(function() {
                    const logoutButton = document.querySelector('li.config-logout');
                    if (logoutButton) {
                      logoutButton.click();
                      console.log('已点击退出登录按钮');
                      
                      // 等待确认对话框出现，然后点击确认
                      setTimeout(function() {
                        const confirmButton = document.querySelector('button.t-dialog__confirm');
                        if (confirmButton) {
                          confirmButton.click();
                          console.log('已点击确认退出按钮');
                        } else {
                          console.log('未找到确认退出按钮');
                          // 尝试方法2
                          tryMethod2();
                        }
                      }, 100);
                    } else {
                      console.log('未找到退出登录按钮');
                      // 尝试方法2
                      tryMethod2();
                    }
                  }, 100);
                } else {
                  console.log('未找到设置按钮');
                  // 尝试方法2
                  tryMethod2();
                }
              } catch (error) {
                console.error('方法1执行失败:', error);
                // 尝试方法2
                tryMethod2();
              }
              
              // 方法2：尝试查找并点击"一键全自动退出"按钮
              function tryMethod2() {
                try {
                  console.log('尝试方法2：查找一键全自动退出按钮');
                  const logoutButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
                    btn.textContent && btn.textContent.includes('退出')
                  );
                  
                  if (logoutButtons.length > 0) {
                    // 找到最可能的退出按钮
                    const logoutButton = logoutButtons.find(btn => 
                      btn.textContent.includes('全自动') || 
                      btn.textContent.includes('一键')
                    ) || logoutButtons[0];
                    
                    logoutButton.click();
                    console.log('已点击找到的退出按钮:', logoutButton.textContent);
                  } else {
                    console.log('未找到任何退出按钮，尝试方法3');
                    tryMethod3();
                  }
                } catch (error) {
                  console.error('方法2执行失败:', error);
                  tryMethod3();
                }
              }
              
              // 方法3：尝试直接清除登录状态并刷新页面
              function tryMethod3() {
                try {
                  console.log('尝试方法3：清除登录状态并刷新页面');
                  
                  // 清除所有可能的登录状态cookie
                  const cookies = document.cookie.split(';');
                  for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i];
                    const eqPos = cookie.indexOf('=');
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                    if (name && (name.includes('login') || name.includes('token') || name.includes('session'))) {
                      document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
                    }
                  }
                  
                  // 清除localStorage中的登录状态和店铺状态
                  const keysToRemove = [];
                  for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (
                      key.includes('login') ||
                      key.includes('token') ||
                      key.includes('session') ||
                      key.includes('shop') ||
                      key.includes('store') ||
                      key.includes('xmh_') ||
                      key.includes('xiaomeihua')
                    ) && key !== 'xmh_shop_mismatch') {
                      keysToRemove.push(key);
                    }
                  }

                  // 批量移除
                  keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                    console.log('webview清除localStorage项: ' + key);
                  });

                  // 【新增】清除sessionStorage中的店铺相关项
                  const sessionKeysToRemove = [];
                  for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (key && (
                      key.includes('shop') ||
                      key.includes('store') ||
                      key.includes('xmh_') ||
                      key.includes('xiaomeihua')
                    )) {
                      sessionKeysToRemove.push(key);
                    }
                  }

                  // 批量移除sessionStorage项
                  sessionKeysToRemove.forEach(key => {
                    sessionStorage.removeItem(key);
                    console.log('webview清除sessionStorage项: ' + key);
                  });
                  
                  // 刷新页面
                  setTimeout(function() {
                    window.location.reload();
                  }, 100);
                } catch (error) {
                  console.error('方法3执行失败:', error);
                  console.log('所有退出方法均失败');
                }
              }
            }

            // 注意：不再立即执行退出操作，只有用户点击"我知道了"按钮后才会执行
            // performLogout(); // 已移除立即执行
          })();
        `);
      })
      .catch(err => {
        console.error('检测店铺名称匹配失败:', err);
      });
    }
  
    // 【修复】添加IPC事件防抖机制
    let ipcCreateTabTimeout = null;
    let lastIpcCreateTabUrl = null;
    let lastIpcCreateTabTime = 0;

    // 监听新标签页创建事件
    const removeCreateTabListener = window.xiaomeihuaAPI.onCreateTab((data) => {
      if (data && data.url) {
        const currentTime = Date.now();

        // 防抖：如果是相同URL且在300ms内，则忽略
        if (lastIpcCreateTabUrl === data.url && (currentTime - lastIpcCreateTabTime) < 300) {
          console.log(`IPC防抖：忽略重复的创建标签页请求: ${data.url}`);
          return;
        }

        lastIpcCreateTabUrl = data.url;
        lastIpcCreateTabTime = currentTime;

        createTab(data.title || '新页面', data.url);
      }
    });

    // 监听webview新窗口请求
    const removeWebviewNewWindowListener = window.xiaomeihuaAPI.onWebviewNewWindow((url) => {
      console.log('接收到webview新窗口请求:', url);
      const currentTime = Date.now();

      // 防抖：如果是相同URL且在300ms内，则忽略
      if (lastIpcCreateTabUrl === url && (currentTime - lastIpcCreateTabTime) < 300) {
        console.log(`Webview防抖：忽略重复的新窗口请求: ${url}`);
        return;
      }

      lastIpcCreateTabUrl = url;
      lastIpcCreateTabTime = currentTime;

      createTab('新页面', url);
    });
  
    // 监听高优先级标签页创建事件
    const removeCreateTabHighPriorityListener = window.xiaomeihuaAPI.onCreateTabHighPriority((data) => {
      if (data && data.url) {
        const currentTime = Date.now();

        // 防抖：如果是相同URL且在300ms内，则忽略
        if (lastIpcCreateTabUrl === data.url && (currentTime - lastIpcCreateTabTime) < 300) {
          console.log(`高优先级防抖：忽略重复的创建标签页请求: ${data.url}`);
          return;
        }

        lastIpcCreateTabUrl = data.url;
        lastIpcCreateTabTime = currentTime;

        console.log('收到高优先级标签页创建请求:', data.url, data.title,
          data.isBottomLink ? '(底部链接)' : '',
          data.isSpecialStoreUrl ? '(特殊商城链接)' : '',
          data.isVideoChannelLink ? '(视频号助手链接)' : '');
      
        // 检查是否是特定的微信商城链接
        const storeUrls = [
          'store.weixin.qq.com/shop/home',
          'store.weixin.qq.com/shop/order/list',
          'store.weixin.qq.com/shop/order/detail'
        ];
      
        // 如果没有传入isSpecialStoreUrl，自己检查一下
        let isSpecialStoreUrl = data.isSpecialStoreUrl;
        if (isSpecialStoreUrl === undefined) {
          isSpecialStoreUrl = storeUrls.some(storeUrl => data.url.includes(storeUrl));
        }
      
        // 检查是否是视频号助手页面链接
        let isVideoChannelLink = data.isVideoChannelLink;
        if (isVideoChannelLink === undefined) {
          isVideoChannelLink = data.url.includes('channels.weixin.qq.com');
        }
      
        // 使用特殊方式处理底部链接、特定微信商城链接或视频号助手链接
        if (data.isBottomLink || isSpecialStoreUrl || isVideoChannelLink) {
          // 直接在当前标签页中打开URL
          if (tabs.length > 0 && activeTabId) {
            const activeTab = tabs.find(t => t.id === activeTabId);
            if (activeTab && activeTab.viewElement) {
              const webview = activeTab.viewElement.querySelector('webview');
              if (webview) {
                console.log('在当前标签页中打开链接:', data.url);

                // 【关键修复】避免在店铺切换期间设置webview.src
                if (!isShopSwitching) {
                  webview.src = data.url;
                  activeTab.title = data.title || '新页面';
                  activeTab.url = data.url;
                  if (activeTab.element && activeTab.element.querySelector('.tab-title')) {
                    activeTab.element.querySelector('.tab-title').textContent = data.title || '新页面';
                  }
                } else {
                  console.log('店铺切换期间，延迟链接导航');
                }
                return;
              }
            }
          }
        }
      
        // 如果无法在当前标签页中打开，则创建新标签页
        createTab(data.title || '新页面', data.url);
      }
    });
  
    // 在页面卸载前移除事件监听器
    window.addEventListener('beforeunload', () => {
      removeShopInfoListener();
      removeCreateTabListener();
      removeWebviewNewWindowListener();
      if (removeCreateTabHighPriorityListener) removeCreateTabHighPriorityListener();
      if (removeForceShopDetectionListener) removeForceShopDetectionListener();
    });
  
    // 初始化
    init();

    // 【彻底修复数据互通问题】移除全局唯一性监控，每个店铺完全独立

    // 【最终修复】恢复并强化对postMessage的监听，这是接收<webview>内部消息的唯一通道
    window.addEventListener('message', (event) => {
      // 此监听器现在作为备用，主要逻辑已移至 page-title-updated
      if (event.data && event.data.type === 'xiaomeihua-new-window' && event.data.url) {
        const currentTime = Date.now();

        // 防抖：如果是相同URL且在300ms内，则忽略
        if (lastIpcCreateTabUrl === event.data.url && (currentTime - lastIpcCreateTabTime) < 300) {
          console.log(`PostMessage防抖：忽略重复的新窗口请求: ${event.data.url}`);
          return;
        }

        lastIpcCreateTabUrl = event.data.url;
        lastIpcCreateTabTime = currentTime;

        console.log(`[Renderer][备用通道] 收到来自<webview>的新窗口请求, URL: ${event.data.url}`);
        window.xiaomeihuaAPI.openUrl(event.data.url, event.data.title || '新页面');
      }
    });
  
    // 【新增】监听清理webview缓存事件
    window.xiaomeihuaAPI.onClearAllWebviewCache(() => {
      console.log('🧹 收到清理所有webview缓存事件');
      clearAllWebviewCache();
    });

    // 【新增】监听完全重置webview事件
    window.xiaomeihuaAPI.onResetAllWebviews(() => {
      console.log('🔄 收到完全重置所有webview事件');
      resetAllWebviews();
    });

    // 【新增】监听强制店铺检测事件
    const removeForceShopDetectionListener = window.xiaomeihuaAPI.onForceShopDetection((detectionData) => {
      console.log('🏪 收到强制店铺检测事件:', detectionData);

      // 清除之前的检测状态
      if (detectionData.clearPreviousState) {
        clearShopCheckStatus();
        console.log('✅ 已清除之前的店铺检测状态');
      }

      // 【新增】如果是新登录，清理所有webview缓存并标记最近登录
      if (detectionData.isNewLogin) {
        console.log('🆕 检测到新登录，清理所有webview缓存并标记最近登录...');
        clearAllWebviewCache();
        markRecentLogin(); // 标记最近登录时间
      }

      // 强制执行店铺检测
      if (detectionData.forceDetection) {
        console.log('🔍 开始强制执行店铺检测...');

        // 获取当前活跃的webview
        const activeTab = tabs.find(tab => tab.active);
        if (activeTab && activeTab.webview) {
          // 延迟执行强制店铺检测，确保页面完全加载
          setTimeout(() => {
            console.log('执行延迟强制店铺检测...');
            checkShopNameMatch(activeTab.webview, true); // 强制检测
          }, 2000);

          // 再次延迟执行，确保店铺信息完全加载
          setTimeout(() => {
            console.log('执行二次延迟强制店铺检测...');
            checkShopNameMatch(activeTab.webview, true); // 强制检测
          }, 5000);

          // 【新增】第三次延迟执行，确保完全检测
          setTimeout(() => {
            console.log('执行三次延迟强制店铺检测...');
            checkShopNameMatch(activeTab.webview, true); // 强制检测
          }, 8000);
        } else {
          console.log('未找到活跃的webview，无法执行店铺检测');
        }
      }
    });

    // 监听店铺信息更新
    const removeUpdateShopInfoListener = window.xiaomeihuaAPI.onUpdateShopInfo((updatedShopInfo) => {
      console.log('收到店铺信息更新:', updatedShopInfo);

      // 更新全局店铺信息
      shopInfo = updatedShopInfo;

      // 【修复】只有在当前没有选择店铺时才设置默认店铺ID，避免覆盖用户的切换选择
      if (!currentShopId) {
        currentShopId = updatedShopInfo.shopId;
        console.log('设置默认店铺ID:', currentShopId);
      } else {
        console.log('保持用户选择的店铺ID:', currentShopId);
      }
    
      // 处理店铺列表
      let shops = [];
      if (updatedShopInfo.shops && updatedShopInfo.shops.length > 0) {
        shops = updatedShopInfo.shops;
      } else {
        // 单店铺情况
        shops = [{
          id: updatedShopInfo.shopId,
          name: updatedShopInfo.shopName,
          wechatStoreId: updatedShopInfo.wechatStoreId || '未知'
        }];
      }
    
      // 更新顶部导航栏的店铺列表
      if (shopsContainer) {
        updateTopShopsList(shops);
        // 【新增】确保当前选择的店铺状态正确显示
        if (currentShopId) {
          updateTopShopsActiveState(currentShopId);
        }
      }

      // 【新增】根据店铺数量控制切换店铺按钮的显示
      updateShopSwitcherVisibility(shops.length);
    
      // 更新设置中的店铺列表
      if (shopList) {
        updateShopList(shops);
      }
    
      // 店铺名称显示已移除
      // if (settingsShopName) {
      //   settingsShopName.textContent = updatedShopInfo.shopName || '未知店铺';
      // }
    });

    // 用户协议弹窗功能
    const agreementModal = document.getElementById('agreement-modal');
    const agreementClose = document.getElementById('agreement-close');
    const settingsAgreementLink = document.getElementById('settings-agreement-link');
    const settingsAgreementTitle = document.getElementById('settings-agreement-title');

    // 预加载协议标题的函数
    async function preloadMainAgreementTitle() {
      try {
        console.log('开始预加载协议标题...');

        if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.getPublishedAgreements) {
          console.log('调用getPublishedAgreements API...');
          const result = await window.xiaomeihuaAPI.getPublishedAgreements();
          console.log('协议API响应:', result);

          if (result.success && result.agreements && result.agreements.length > 0) {
            console.log(`找到 ${result.agreements.length} 个协议`);

            // 更新协议容器
            const agreementsContainer = document.getElementById('agreements-container');
            if (agreementsContainer) {
              // 清空现有内容
              agreementsContainer.innerHTML = '';

              // 为每个协议创建独立的小模块
              result.agreements.forEach((agreement, index) => {
                console.log(`创建协议模块: ${agreement.title}`);
                const agreementModule = document.createElement('div');
                agreementModule.className = 'agreement-module';
                agreementModule.style.cssText = `
                  display: inline-block;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white;
                  padding: 12px 20px;
                  border-radius: 12px;
                  cursor: pointer;
                  text-decoration: none;
                  font-size: 14px;
                  font-weight: 600;
                  transition: all 0.3s ease;
                  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                  border: none;
                  min-width: 120px;
                  text-align: center;
                `;
                agreementModule.innerHTML = `<span>${agreement.title}</span>`;
                agreementModule.dataset.agreementId = agreement.id;

                // 添加悬停效果
                agreementModule.addEventListener('mouseenter', () => {
                  agreementModule.style.transform = 'translateY(-2px)';
                  agreementModule.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)';
                });

                agreementModule.addEventListener('mouseleave', () => {
                  agreementModule.style.transform = 'translateY(0)';
                  agreementModule.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.3)';
                });

                // 添加点击事件
                agreementModule.addEventListener('click', () => {
                  console.log('点击协议模块:', agreement.title);
                  loadSpecificAgreementContent(agreement);
                });

                agreementsContainer.appendChild(agreementModule);
              });

              console.log('协议链接创建完成');
            } else {
              console.error('未找到agreements-container元素');
            }
          } else {
            console.log('没有找到已发布的协议');
            // 显示默认提示
            const agreementsContainer = document.getElementById('agreements-container');
            if (agreementsContainer) {
              agreementsContainer.innerHTML = '<div style="color: #666; font-size: 12px;">暂无协议内容</div>';
            }
          }
        } else {
          console.error('协议API不可用');
          const agreementsContainer = document.getElementById('agreements-container');
          if (agreementsContainer) {
            agreementsContainer.innerHTML = '<div style="color: #666; font-size: 12px;">协议功能不可用</div>';
          }
        }
      } catch (error) {
        console.error('预加载协议标题失败:', error);
        // 显示错误提示
        const agreementsContainer = document.getElementById('agreements-container');
        if (agreementsContainer) {
          agreementsContainer.innerHTML = '<div style="color: #666; font-size: 12px;">协议加载失败</div>';
        }
      }
    }

    // 加载特定协议内容的函数 - 修改为使用独立窗口
    async function loadSpecificAgreementContent(agreement) {
      try {
        console.log('打开协议独立窗口:', agreement);

        // 使用IPC调用主进程打开协议窗口
        if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.openAgreementWindow) {
          await window.xiaomeihuaAPI.openAgreementWindow(agreement);
        } else {
          console.error('协议窗口API不可用');
          // 降级处理：显示提示
          alert('协议功能暂时不可用，请稍后重试');
        }
      } catch (error) {
        console.error('打开协议窗口失败:', error);
        alert('打开协议窗口失败，请稍后重试');
      }
    }

    // 加载协议内容的函数（兼容旧版本）
    async function loadAgreementContent() {
      try {
        if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.getPublishedAgreements) {
          const result = await window.xiaomeihuaAPI.getPublishedAgreements();
          if (result.success && result.agreements && result.agreements.length > 0) {
            // 加载第一个协议
            await loadSpecificAgreementContent(result.agreements[0]);
          } else {
            // 没有协议，显示提示
            const agreementText = document.getElementById('agreement-text');
            if (agreementText) {
              agreementText.innerHTML = `
                <div style="text-align: center; padding: 40px 20px; color: #666;">
                  <p>暂无协议内容</p>
                  <p style="font-size: 14px; margin-top: 10px;">请联系管理员设置协议内容</p>
                </div>
              `;
            }
          }
        }
      } catch (error) {
        console.error('加载协议内容失败:', error);
      }
    }

    if (agreementClose) {
      agreementClose.addEventListener('click', () => {
        if (agreementModal) {
          agreementModal.style.display = 'none';
        }
      });
    }

    // 注意：settingsAgreementLink 现在可能不存在，因为协议链接是动态创建的
    // 协议链接的点击事件在 preloadMainAgreementTitle 函数中处理

    // 点击弹窗外部关闭弹窗
    if (agreementModal) {
      agreementModal.addEventListener('click', (e) => {
        if (e.target === agreementModal) {
          agreementModal.style.display = 'none';
        }
      });
    }

    // 【彻底解决矩形边框问题】全局事件监听器
    // 监听所有焦点事件，特别针对店铺标签页
    document.addEventListener('focusin', (e) => {
      if (e.target.classList.contains('shop-item')) {
        e.preventDefault();
        e.stopPropagation();
        e.target.blur();
        e.target.style.outline = '0';
        e.target.style.border = '0';
        e.target.style.boxShadow = 'none';
      }
    });

    // 监听所有点击事件，确保店铺标签页不会获得焦点
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('shop-item')) {
        setTimeout(() => {
          e.target.blur();
          e.target.style.outline = '0';
          e.target.style.border = '0';
          if (document.activeElement === e.target) {
            document.activeElement.blur();
          }
        }, 0);
      }
    });

    // 【新增】监听鼠标悬停事件，彻底解决悬停矩形框问题
    document.addEventListener('mouseover', (e) => {
      if (e.target.classList.contains('shop-item')) {
        e.target.style.outline = '0';
        e.target.style.border = '0';
        e.target.style.boxShadow = 'none';
        e.target.blur();
        if (document.activeElement === e.target) {
          document.activeElement.blur();
        }
      }
    });

    document.addEventListener('mouseenter', (e) => {
      if (e.target.classList.contains('shop-item')) {
        e.target.style.outline = '0';
        e.target.style.border = '0';
        e.target.style.boxShadow = 'none';
        e.target.blur();
      }
    }, true);

    document.addEventListener('mousemove', (e) => {
      if (e.target.classList.contains('shop-item')) {
        e.target.style.outline = '0';
        e.target.style.border = '0';
        e.target.style.boxShadow = 'none';
      }
    });

    // 监听键盘事件，防止Tab键导致的焦点
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        const activeElement = document.activeElement;
        if (activeElement && activeElement.classList.contains('shop-item')) {
          e.preventDefault();
          activeElement.blur();
          activeElement.style.outline = '0';
          activeElement.style.border = '0';
        }
      }
    });

    // 定期清理所有店铺标签页的焦点效果和悬停效果
    setInterval(() => {
      document.querySelectorAll('.shop-item').forEach(item => {
        if (document.activeElement === item) {
          item.blur();
        }
        item.style.outline = '0';
        item.style.border = '0';
        item.style.boxShadow = 'none';
      });
    }, 50); // 缩短间隔到50ms，更频繁地清理

    // 页面加载完成后预加载协议标题
    setTimeout(() => {
      preloadMainAgreementTitle();
    }, 2000);

    // 定期重新加载协议（每30秒检查一次）
    setInterval(() => {
      preloadMainAgreementTitle();
    }, 30000);

    // 窗口获得焦点时重新加载协议
    window.addEventListener('focus', () => {
      setTimeout(() => {
        preloadMainAgreementTitle();
      }, 1000);
    });
  </script>
</body>
</html>
