<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL匹配功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #007cba;
        }
        .result {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .patterns-list {
            margin: 10px 0;
        }
        .pattern-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        .pattern-item input {
            flex: 1;
            margin-right: 10px;
        }
        .pattern-item button {
            padding: 5px 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 URL匹配功能测试工具</h1>
        
        <div class="test-section">
            <h2>手动测试</h2>
            <div class="input-group">
                <label for="testUrl">测试URL:</label>
                <input type="text" id="testUrl" placeholder="例如: https://store.weixin.qq.com/shop/kf" value="https://store.weixin.qq.com/shop/kf">
            </div>
            
            <div class="input-group">
                <label>URL匹配规则:</label>
                <div class="patterns-list" id="patternsList">
                    <div class="pattern-item">
                        <input type="text" placeholder="例如: https://store.weixin.qq.com/*" value="https://store.weixin.qq.com/*">
                        <button onclick="removePattern(this)">删除</button>
                    </div>
                </div>
                <button onclick="addPattern()">添加规则</button>
            </div>
            
            <button onclick="testManual()">测试匹配</button>
            <div id="manualResult"></div>
        </div>

        <div class="test-section">
            <h2>自动化测试用例</h2>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>测试日志</h2>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        // URL匹配算法（与APP中的实现保持一致）
        function matchUrl(url, pattern) {
            if (!pattern || !url) return false;

            // 如果模式是 * 则匹配所有URL
            if (pattern === '*') return true;

            // 使用简单的字符串匹配方法，避免复杂的正则表达式问题
            try {
                if (pattern.includes('*')) {
                    // 如果包含通配符，使用简单的前缀/后缀匹配
                    if (pattern.endsWith('*')) {
                        // 前缀匹配，如 https://store.weixin.qq.com/*
                        const prefix = pattern.slice(0, -1);
                        return url.toLowerCase().startsWith(prefix.toLowerCase());
                    } else if (pattern.startsWith('*')) {
                        // 后缀匹配，如 */shop/kf
                        const suffix = pattern.slice(1);
                        return url.toLowerCase().endsWith(suffix.toLowerCase());
                    } else {
                        // 中间包含通配符，使用更复杂的匹配
                        const parts = pattern.split('*');
                        let currentIndex = 0;
                        for (let i = 0; i < parts.length; i++) {
                            if (parts[i]) {
                                const partIndex = url.toLowerCase().indexOf(parts[i].toLowerCase(), currentIndex);
                                if (partIndex === -1) {
                                    return false;
                                }
                                currentIndex = partIndex + parts[i].length;
                            }
                        }
                        return true;
                    }
                } else {
                    // 精确匹配
                    return url.toLowerCase() === pattern.toLowerCase();
                }
            } catch (e) {
                log(`URL匹配错误: ${e.message}, 模式: ${pattern}`);
                return false;
            }
        }

        function shouldLoadScriptForUrl(url, urlPatterns) {
            if (!url) {
                log('URL为空，不加载脚本');
                return false;
            }
            
            if (!urlPatterns || !Array.isArray(urlPatterns) || urlPatterns.length === 0) {
                log('没有URL规则，默认不加载脚本');
                return false;
            }
            
            log(`检查URL: ${url}`);
            log(`URL规则: ${JSON.stringify(urlPatterns)}`);
            
            for (let i = 0; i < urlPatterns.length; i++) {
                const pattern = urlPatterns[i];
                if (matchUrl(url, pattern)) {
                    log(`✅ 匹配成功: ${url} 匹配规则 "${pattern}"`);
                    return true;
                } else {
                    log(`❌ 不匹配: ${url} 不匹配规则 "${pattern}"`);
                }
            }
            
            log(`❌ 所有规则都不匹配，不加载脚本`);
            return false;
        }

        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        function addPattern() {
            const patternsList = document.getElementById('patternsList');
            const newPattern = document.createElement('div');
            newPattern.className = 'pattern-item';
            newPattern.innerHTML = `
                <input type="text" placeholder="例如: https://store.weixin.qq.com/*">
                <button onclick="removePattern(this)">删除</button>
            `;
            patternsList.appendChild(newPattern);
        }

        function removePattern(button) {
            button.parentElement.remove();
        }

        function getPatterns() {
            const inputs = document.querySelectorAll('#patternsList input');
            const patterns = [];
            inputs.forEach(input => {
                if (input.value.trim()) {
                    patterns.push(input.value.trim());
                }
            });
            return patterns;
        }

        function testManual() {
            const url = document.getElementById('testUrl').value.trim();
            const patterns = getPatterns();
            const result = shouldLoadScriptForUrl(url, patterns);
            
            const resultDiv = document.getElementById('manualResult');
            resultDiv.innerHTML = `
                <div class="result ${result ? 'success' : 'error'}">
                    <strong>测试结果:</strong> ${result ? '✅ 匹配成功，应该加载脚本' : '❌ 不匹配，不应该加载脚本'}
                </div>
                <div class="result info">
                    <strong>测试URL:</strong> ${url}<br>
                    <strong>匹配规则:</strong> ${JSON.stringify(patterns)}
                </div>
            `;
        }

        function runAllTests() {
            const testCases = [
                {
                    name: '精确匹配测试',
                    url: 'https://store.weixin.qq.com/shop/kf',
                    patterns: ['https://store.weixin.qq.com/shop/kf'],
                    expected: true
                },
                {
                    name: '通配符匹配测试',
                    url: 'https://store.weixin.qq.com/shop/kf/123',
                    patterns: ['https://store.weixin.qq.com/*'],
                    expected: true
                },
                {
                    name: '协议通配符测试',
                    url: 'https://store.weixin.qq.com/shop',
                    patterns: ['*://store.weixin.qq.com/*'],
                    expected: true
                },
                {
                    name: '不匹配测试',
                    url: 'https://example.com/test',
                    patterns: ['https://store.weixin.qq.com/*'],
                    expected: false
                },
                {
                    name: '多规则匹配测试',
                    url: 'https://channels.weixin.qq.com/platform',
                    patterns: ['https://store.weixin.qq.com/*', 'https://channels.weixin.qq.com/*'],
                    expected: true
                },
                {
                    name: '全匹配通配符测试',
                    url: 'https://any-website.com/any-path',
                    patterns: ['*'],
                    expected: true
                },
                {
                    name: '空规则测试',
                    url: 'https://store.weixin.qq.com/shop/kf',
                    patterns: [],
                    expected: false
                }
            ];

            let results = '<h3>测试结果:</h3>';
            let passCount = 0;
            
            testCases.forEach((testCase, index) => {
                log(`\n=== 运行测试 ${index + 1}: ${testCase.name} ===`);
                const result = shouldLoadScriptForUrl(testCase.url, testCase.patterns);
                const passed = result === testCase.expected;
                
                if (passed) passCount++;
                
                results += `
                    <div class="test-case">
                        <strong>${testCase.name}</strong>
                        <div class="result ${passed ? 'success' : 'error'}">
                            ${passed ? '✅ 通过' : '❌ 失败'} - 
                            预期: ${testCase.expected ? '匹配' : '不匹配'}, 
                            实际: ${result ? '匹配' : '不匹配'}
                        </div>
                        <div style="font-size: 12px; color: #666;">
                            URL: ${testCase.url}<br>
                            规则: ${JSON.stringify(testCase.patterns)}
                        </div>
                    </div>
                `;
            });

            results += `
                <div class="result ${passCount === testCases.length ? 'success' : 'error'}">
                    <strong>总结:</strong> ${passCount}/${testCases.length} 个测试通过
                </div>
            `;

            document.getElementById('testResults').innerHTML = results;
        }

        // 页面加载完成后运行一次测试
        window.addEventListener('load', () => {
            log('URL匹配功能测试工具已加载');
            log('点击"运行所有测试"按钮开始自动化测试');
        });
    </script>
</body>
</html>
