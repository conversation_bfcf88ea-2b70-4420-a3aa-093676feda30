// 简单的脚本注入测试
console.log('🧪 开始简单脚本注入测试...');

// 模拟后台配置的URL规则
const urlRules = [
  'https://filehelper.weixin.qq.com/',
  'https://store.weixin.qq.com/shop/kf*'
];

// 模拟当前访问的URL
const testUrls = [
  'https://store.weixin.qq.com/shop/kf?shop_id=wxe82223181f888d3f',
  'https://filehelper.weixin.qq.com/',
  'https://filehelper.weixin.qq.com/upload',
  'https://example.com/test'
];

// 简单的URL匹配函数
function simpleMatchUrl(url, pattern) {
  if (!pattern || !url) return false;
  
  // 如果模式是 * 则匹配所有URL
  if (pattern === '*') return true;
  
  // 简单的通配符匹配
  if (pattern.includes('*')) {
    // 将通配符替换为正则表达式
    const regexPattern = pattern.replace(/\*/g, '.*');
    const regex = new RegExp('^' + regexPattern + '$', 'i');
    return regex.test(url);
  }
  
  // 精确匹配
  return url.toLowerCase() === pattern.toLowerCase();
}

// 检查URL是否应该注入脚本
function shouldInjectScript(url, rules) {
  for (const rule of rules) {
    if (simpleMatchUrl(url, rule)) {
      return true;
    }
  }
  return false;
}

// 测试URL匹配
console.log('\n=== URL匹配测试 ===');
testUrls.forEach(url => {
  const shouldInject = shouldInjectScript(url, urlRules);
  console.log(`URL: ${url}`);
  console.log(`应该注入脚本: ${shouldInject ? '✅ 是' : '❌ 否'}`);
  
  if (shouldInject) {
    // 找到匹配的规则
    const matchedRule = urlRules.find(rule => simpleMatchUrl(url, rule));
    console.log(`匹配的规则: ${matchedRule}`);
  }
  console.log('---');
});

console.log('\n✅ 简单脚本注入测试完成');
