/**
 * 脚本加载功能测试脚本
 * 用于验证URL匹配功能是否正常工作
 */

console.log('🧪 脚本加载功能测试开始...');

// 测试URL匹配算法
function testUrlMatching() {
    console.log('\n=== URL匹配算法测试 ===');
    
    // 模拟matchUrl函数
    function matchUrl(url, pattern) {
        if (!pattern || !url) return false;

        // 如果模式是 * 则匹配所有URL
        if (pattern === '*') return true;

        // 使用简单的字符串匹配方法，避免复杂的正则表达式问题
        try {
            if (pattern.includes('*')) {
                // 如果包含通配符，使用简单的前缀/后缀匹配
                if (pattern.endsWith('*')) {
                    // 前缀匹配，如 https://store.weixin.qq.com/*
                    const prefix = pattern.slice(0, -1);
                    return url.toLowerCase().startsWith(prefix.toLowerCase());
                } else if (pattern.startsWith('*')) {
                    // 后缀匹配，如 */shop/kf
                    const suffix = pattern.slice(1);
                    return url.toLowerCase().endsWith(suffix.toLowerCase());
                } else {
                    // 中间包含通配符，使用更复杂的匹配
                    const parts = pattern.split('*');
                    let currentIndex = 0;
                    for (let i = 0; i < parts.length; i++) {
                        if (parts[i]) {
                            const partIndex = url.toLowerCase().indexOf(parts[i].toLowerCase(), currentIndex);
                            if (partIndex === -1) {
                                return false;
                            }
                            currentIndex = partIndex + parts[i].length;
                        }
                    }
                    return true;
                }
            } else {
                // 精确匹配
                return url.toLowerCase() === pattern.toLowerCase();
            }
        } catch (e) {
            console.error(`URL匹配错误: ${e.message}, 模式: ${pattern}`);
            return false;
        }
    }

    // 测试用例
    const testCases = [
        {
            name: '精确匹配',
            url: 'https://store.weixin.qq.com/shop/kf',
            pattern: 'https://store.weixin.qq.com/shop/kf',
            expected: true
        },
        {
            name: '通配符匹配',
            url: 'https://store.weixin.qq.com/shop/kf/123',
            pattern: 'https://store.weixin.qq.com/*',
            expected: true
        },
        {
            name: '协议通配符',
            url: 'https://store.weixin.qq.com/shop',
            pattern: '*://store.weixin.qq.com/*',
            expected: true
        },
        {
            name: '不匹配情况',
            url: 'https://example.com/test',
            pattern: 'https://store.weixin.qq.com/*',
            expected: false
        },
        {
            name: '全匹配通配符',
            url: 'https://any-website.com/any-path',
            pattern: '*',
            expected: true
        }
    ];

    let passCount = 0;
    testCases.forEach((testCase, index) => {
        const result = matchUrl(testCase.url, testCase.pattern);
        const passed = result === testCase.expected;
        
        console.log(`测试 ${index + 1}: ${testCase.name}`);
        console.log(`  URL: ${testCase.url}`);
        console.log(`  规则: ${testCase.pattern}`);
        console.log(`  预期: ${testCase.expected}, 实际: ${result}`);
        console.log(`  结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
        
        if (passed) passCount++;
    });

    console.log(`\n测试总结: ${passCount}/${testCases.length} 个测试通过`);
    return passCount === testCases.length;
}

// 测试shouldLoadScriptForUrl函数
function testShouldLoadScript() {
    console.log('\n=== 脚本加载决策测试 ===');
    
    function shouldLoadScriptForUrl(url, urlPatterns) {
        if (!url) {
            console.log('URL为空，不加载脚本');
            return false;
        }
        
        if (!urlPatterns || !Array.isArray(urlPatterns) || urlPatterns.length === 0) {
            console.log('没有URL规则，默认不加载脚本');
            return false;
        }
        
        // 简化的matchUrl实现
        function matchUrl(url, pattern) {
            if (!pattern || !url) return false;
            if (pattern === '*') return true;

            // 使用简单的字符串匹配方法
            try {
                if (pattern.includes('*')) {
                    if (pattern.endsWith('*')) {
                        const prefix = pattern.slice(0, -1);
                        return url.toLowerCase().startsWith(prefix.toLowerCase());
                    } else if (pattern.startsWith('*')) {
                        const suffix = pattern.slice(1);
                        return url.toLowerCase().endsWith(suffix.toLowerCase());
                    } else {
                        const parts = pattern.split('*');
                        let currentIndex = 0;
                        for (let i = 0; i < parts.length; i++) {
                            if (parts[i]) {
                                const partIndex = url.toLowerCase().indexOf(parts[i].toLowerCase(), currentIndex);
                                if (partIndex === -1) {
                                    return false;
                                }
                                currentIndex = partIndex + parts[i].length;
                            }
                        }
                        return true;
                    }
                } else {
                    return url.toLowerCase() === pattern.toLowerCase();
                }
            } catch (e) {
                console.error(`URL匹配错误: ${e.message}, 模式: ${pattern}`);
                return false;
            }
        }
        
        for (let i = 0; i < urlPatterns.length; i++) {
            const pattern = urlPatterns[i];
            if (matchUrl(url, pattern)) {
                console.log(`✅ 匹配成功: ${url} 匹配规则 "${pattern}"`);
                return true;
            }
        }
        
        console.log(`❌ 所有规则都不匹配，不加载脚本`);
        return false;
    }

    const testCases = [
        {
            name: 'AI智能客服页面匹配',
            url: 'https://store.weixin.qq.com/shop/kf',
            patterns: ['https://store.weixin.qq.com/*'],
            expected: true
        },
        {
            name: 'AI智能上架页面匹配',
            url: 'https://filehelper.weixin.qq.com/',
            patterns: ['https://filehelper.weixin.qq.com/*'],
            expected: true
        },
        {
            name: '视频号助手页面匹配',
            url: 'https://channels.weixin.qq.com/platform',
            patterns: ['https://channels.weixin.qq.com/*'],
            expected: true
        },
        {
            name: '多规则匹配',
            url: 'https://store.weixin.qq.com/shop/kf',
            patterns: [
                'https://store.weixin.qq.com/*',
                'https://filehelper.weixin.qq.com/*',
                'https://channels.weixin.qq.com/*'
            ],
            expected: true
        },
        {
            name: '不匹配的页面',
            url: 'https://example.com/test',
            patterns: ['https://store.weixin.qq.com/*'],
            expected: false
        },
        {
            name: '空规则数组',
            url: 'https://store.weixin.qq.com/shop/kf',
            patterns: [],
            expected: false
        },
        {
            name: '无效规则',
            url: 'https://store.weixin.qq.com/shop/kf',
            patterns: null,
            expected: false
        }
    ];

    let passCount = 0;
    testCases.forEach((testCase, index) => {
        console.log(`\n--- 测试 ${index + 1}: ${testCase.name} ---`);
        const result = shouldLoadScriptForUrl(testCase.url, testCase.patterns);
        const passed = result === testCase.expected;
        
        console.log(`预期: ${testCase.expected}, 实际: ${result}`);
        console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
        
        if (passed) passCount++;
    });

    console.log(`\n脚本加载决策测试总结: ${passCount}/${testCases.length} 个测试通过`);
    return passCount === testCases.length;
}

// 模拟API响应测试
function testApiResponse() {
    console.log('\n=== API响应处理测试 ===');
    
    const mockApiResponses = [
        {
            name: '正常响应带URL规则',
            response: {
                success: true,
                script: 'console.log("测试脚本");',
                url_patterns: ['https://store.weixin.qq.com/*', 'https://filehelper.weixin.qq.com/*']
            },
            currentUrl: 'https://store.weixin.qq.com/shop/kf',
            expectedLoad: true
        },
        {
            name: '正常响应但URL不匹配',
            response: {
                success: true,
                script: 'console.log("测试脚本");',
                url_patterns: ['https://store.weixin.qq.com/*']
            },
            currentUrl: 'https://example.com/test',
            expectedLoad: false
        },
        {
            name: '响应无URL规则（向后兼容）',
            response: {
                success: true,
                script: 'console.log("测试脚本");',
                url_patterns: []
            },
            currentUrl: 'https://store.weixin.qq.com/shop/kf',
            expectedLoad: true // 向后兼容，应该加载
        },
        {
            name: '响应失败',
            response: {
                success: false,
                message: '验证失败'
            },
            currentUrl: 'https://store.weixin.qq.com/shop/kf',
            expectedLoad: false
        }
    ];

    let passCount = 0;
    mockApiResponses.forEach((testCase, index) => {
        console.log(`\n--- API测试 ${index + 1}: ${testCase.name} ---`);
        
        const response = testCase.response;
        const currentUrl = testCase.currentUrl;
        
        let shouldLoad = false;
        
        if (response && response.success && response.script) {
            const urlPatterns = response.url_patterns || [];
            
            if (!urlPatterns || !Array.isArray(urlPatterns) || urlPatterns.length === 0) {
                console.log('⚠️ 无URL规则，向后兼容模式');
                shouldLoad = true;
            } else {
                // 简化的URL匹配检查
                shouldLoad = urlPatterns.some(pattern => {
                    if (pattern === '*') return true;
                    return currentUrl.includes(pattern.replace('*', ''));
                });
            }
        }
        
        const passed = shouldLoad === testCase.expectedLoad;
        console.log(`当前URL: ${currentUrl}`);
        console.log(`URL规则: ${JSON.stringify(response.url_patterns)}`);
        console.log(`预期加载: ${testCase.expectedLoad}, 实际决策: ${shouldLoad}`);
        console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
        
        if (passed) passCount++;
    });

    console.log(`\nAPI响应处理测试总结: ${passCount}/${mockApiResponses.length} 个测试通过`);
    return passCount === mockApiResponses.length;
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始运行所有测试...\n');
    
    const results = [
        testUrlMatching(),
        testShouldLoadScript(),
        testApiResponse()
    ];
    
    const totalPassed = results.filter(r => r).length;
    const totalTests = results.length;
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 最终测试结果');
    console.log('='.repeat(50));
    console.log(`总测试模块: ${totalTests}`);
    console.log(`通过模块: ${totalPassed}`);
    console.log(`失败模块: ${totalTests - totalPassed}`);
    console.log(`成功率: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);
    
    if (totalPassed === totalTests) {
        console.log('🎉 所有测试通过！URL匹配功能工作正常。');
    } else {
        console.log('⚠️ 部分测试失败，请检查实现。');
    }
    
    return totalPassed === totalTests;
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.runUrlMatchingTests = runAllTests;
    console.log('测试函数已注册到 window.runUrlMatchingTests');
    console.log('在控制台中运行 runUrlMatchingTests() 开始测试');
} else {
    // 如果在Node.js环境中运行
    runAllTests();
}
